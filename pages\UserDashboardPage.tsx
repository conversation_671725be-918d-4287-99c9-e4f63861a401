
import React, { useState, useEffect, ChangeEvent, FormEvent } from 'react';
// Fix: Import Link from react-router-dom.
import { Link } from 'react-router-dom';
import { PageContainer }  from '../components/Layout';
import { useApp } from '../AppContext';
import { User, UserRole, Post, Event, SocialLinks, PostCategory, Lesson } from '../types';
import { Button, Input, Textarea, Select, Card, Avatar, Banner, Tag, ProfileSection, FeedItem, EventItem, Icon, Modal, SocialLinkItem, Tabs } from '../components';

const UserDashboardPage: React.FC = () => {
  const {
    currentUser, updateUserProfile, getPosts, createPost, getEvents, createEvent, getUserById,
    createLesson, updateLesson, deleteLesson, getLessons
  } = useApp();
  const [user, setUser] = useState<User | null>(currentUser);
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState<Partial<User>>({});

  const [isCreatePostModalOpen, setIsCreatePostModalOpen] = useState(false);
  const [newPostData, setNewPostData] = useState<Partial<Omit<Post, 'id' | 'likes' | 'createdAt' | 'comments'>>>({ userId: currentUser?.id, mediaType: 'image', category: PostCategory.VISUAL_ART, tags: [] });

  const [isCreateEventModalOpen, setIsCreateEventModalOpen] = useState(false);
  const [newEventData, setNewEventData] = useState<Partial<Omit<Event, 'id' | 'rsvps' | 'createdAt'>>>({ educatorId: currentUser?.id, format: 'virtual' });

  const [isCreateLessonModalOpen, setIsCreateLessonModalOpen] = useState(false);
  const [newLessonData, setNewLessonData] = useState<Partial<Omit<Lesson, 'id' | 'createdAt'>>>({ educatorId: currentUser?.id });
  const [editingLesson, setEditingLesson] = useState<Lesson | null>(null);

  const [userPosts, setUserPosts] = useState<Post[]>([]);
  const [userEvents, setUserEvents] = useState<Event[]>([]);
  const [userLessons, setUserLessons] = useState<Lesson[]>([]);

  useEffect(() => {
    setUser(currentUser);
    setEditData(currentUser || {});
    if (currentUser) {
      if (currentUser.role === UserRole.ARTIST) {
        setUserPosts(getPosts({ userId: currentUser.id }));
      }
      if (currentUser.role === UserRole.EDUCATOR) {
        setUserEvents(getEvents({ educatorId: currentUser.id }));
        setUserLessons(getLessons({ educatorId: currentUser.id }));
      }
    }
  }, [currentUser, getPosts, getEvents, getLessons]);

  if (!user) {
    return <PageContainer title="Dashboard"><p>Loading user data or not logged in...</p></PageContainer>;
  }

  const handleInputChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    if (name.startsWith('socialLinks.')) {
        const platform = name.split('.')[1] as keyof SocialLinks;
        setEditData(prev => ({ ...prev, socialLinks: { ...prev.socialLinks, [platform]: value } }));
    } else if (name === 'tags' || name === 'genre') {
        setEditData(prev => ({ ...prev, [name]: value.split(',').map(tag => tag.trim()).filter(tag => tag) }));
    } else {
        setEditData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleProfileSave = async (e: FormEvent) => {
    e.preventDefault();
    if (currentUser) {
      const updated = await updateUserProfile(currentUser.id, editData);
      if (updated) {
        setUser(updated);
        setIsEditing(false);
      } else {
        alert("Profile update failed.");
      }
    }
  };

  const handleCreatePost = async (e: FormEvent) => {
    e.preventDefault();
    if (!currentUser || !newPostData.title || !newPostData.mediaUrl || !newPostData.userId) {
        alert("Please fill all required fields for the post.");
        return;
    }
    const result = await createPost(newPostData as Omit<Post, 'id' | 'likes' | 'createdAt'>);
    if (result) {
        setUserPosts(prev => [result, ...prev]);
        setIsCreatePostModalOpen(false);
        setNewPostData({ userId: currentUser.id, mediaType: 'image', category: PostCategory.VISUAL_ART, tags: [] });
    } else {
        alert("Failed to create post.");
    }
  };
  
  const handleCreateEvent = async (e: FormEvent) => {
    e.preventDefault();
    if (!currentUser || !newEventData.title || !newEventData.description || !newEventData.dateTime || !newEventData.locationOrLink || !newEventData.educatorId) {
        alert("Please fill all required fields for the event.");
        return;
    }
    const result = await createEvent(newEventData as Omit<Event, 'id' | 'rsvps' | 'createdAt'>);
    if (result) {
        setUserEvents(prev => [result, ...prev]);
        setIsCreateEventModalOpen(false);
        setNewEventData({ educatorId: currentUser.id, format: 'virtual' });
    } else {
        alert("Failed to create event.");
    }
  };

  const handleCreateLesson = async (e: FormEvent) => {
    e.preventDefault();
    if (!currentUser || !newLessonData.title || !newLessonData.description || !newLessonData.educatorId) {
        alert("Please fill all required fields for the lesson.");
        return;
    }
    const result = await createLesson(newLessonData as Omit<Lesson, 'id' | 'createdAt'>);
    if (result) {
        setUserLessons(prev => [result, ...prev]);
        setIsCreateLessonModalOpen(false);
        setNewLessonData({ educatorId: currentUser.id });
    } else {
        alert("Failed to create lesson.");
    }
  };

  const handleUpdateLesson = async (e: FormEvent) => {
    e.preventDefault();
    if (!editingLesson || !editingLesson.title || !editingLesson.description) {
        alert("Please fill all required fields for the lesson.");
        return;
    }
    const result = await updateLesson(editingLesson.id, editingLesson);
    if (result) {
        setUserLessons(prev => prev.map(lesson =>
          lesson.id === editingLesson.id ? editingLesson : lesson
        ));
        setEditingLesson(null);
    } else {
        alert("Failed to update lesson.");
    }
  };

  const handleDeleteLesson = async (lessonId: string) => {
    if (confirm("Are you sure you want to delete this lesson?")) {
      const result = await deleteLesson(lessonId);
      if (result) {
        setUserLessons(prev => prev.filter(lesson => lesson.id !== lessonId));
      } else {
        alert("Failed to delete lesson.");
      }
    }
  };

  const ProfileDisplay = () => (
    <Card className="p-6">
      <div className="flex flex-col sm:flex-row items-center sm:items-start">
        <Avatar src={user.profilePhotoUrl} alt={user.displayName} size="lg" className="mb-4 sm:mb-0 sm:mr-6" />
        <div className="text-center sm:text-left">
          <h2 className="text-3xl font-bold text-neutral-darkest">{user.displayName}</h2>
          <p className="text-md text-primary">{user.role}</p>
          {user.location && <p className="text-sm text-neutral-dark mt-1"><Icon name="mapPin" className="inline w-4 h-4 mr-1" />{user.location}</p>}
          <div className="mt-2">
            {user.tags.map(tag => <Tag key={tag}>{tag}</Tag>)}
          </div>
        </div>
      </div>
      {user.bio && <p className="mt-4 text-neutral-dark text-center sm:text-left">{user.bio}</p>}
      <div className="mt-4 flex justify-center sm:justify-start">
          <SocialLinkItem platform="Instagram" url={user.socialLinks?.instagram} iconName="link" />
          <SocialLinkItem platform="TikTok" url={user.socialLinks?.tiktok} iconName="link" />
          <SocialLinkItem platform="YouTube" url={user.socialLinks?.youtube} iconName="link" />
          <SocialLinkItem platform="SoundCloud" url={user.socialLinks?.soundcloud} iconName="link" />
          <SocialLinkItem platform="Spotify" url={user.socialLinks?.spotify} iconName="link" />
      </div>
      <Button onClick={() => setIsEditing(true)} variant="outline" className="mt-6 w-full sm:w-auto">Edit Profile</Button>
    </Card>
  );

  const ProfileEditForm = () => (
    <Card className="p-6">
      <form onSubmit={handleProfileSave} className="space-y-4">
        <h2 className="text-2xl font-semibold mb-4">Edit Profile</h2>
        <Input label="Display Name" name="displayName" value={editData.displayName || ''} onChange={handleInputChange} />
        <Input label="Profile Photo URL" name="profilePhotoUrl" value={editData.profilePhotoUrl || ''} onChange={handleInputChange} placeholder="https://example.com/photo.jpg" />
        <Input label="Banner Image URL" name="bannerImageUrl" value={editData.bannerImageUrl || ''} onChange={handleInputChange} placeholder="https://example.com/banner.jpg" />
        <Textarea label="Bio" name="bio" value={editData.bio || ''} onChange={handleInputChange} />
        <Input label="Location (City, Country)" name="location" value={editData.location || ''} onChange={handleInputChange} />
        <Input label="Tags (comma-separated)" name="tags" value={editData.tags?.join(', ') || ''} onChange={handleInputChange} />
        
        <h3 className="text-lg font-medium pt-2">Social Links</h3>
        <Input label="Instagram URL" name="socialLinks.instagram" value={editData.socialLinks?.instagram || ''} onChange={handleInputChange} />
        <Input label="TikTok URL" name="socialLinks.tiktok" value={editData.socialLinks?.tiktok || ''} onChange={handleInputChange} />
        <Input label="YouTube URL" name="socialLinks.youtube" value={editData.socialLinks?.youtube || ''} onChange={handleInputChange} />
        <Input label="SoundCloud URL" name="socialLinks.soundcloud" value={editData.socialLinks?.soundcloud || ''} onChange={handleInputChange} />
        <Input label="Spotify URL" name="socialLinks.spotify" value={editData.socialLinks?.spotify || ''} onChange={handleInputChange} />

        {user.role === UserRole.ARTIST && (
          <>
            <h3 className="text-lg font-medium pt-2">Artist Details</h3>
            <Textarea label="Artist Statement" name="artistStatement" value={editData.artistStatement || ''} onChange={handleInputChange} />
            <Input label="Genres/Styles (comma-separated)" name="genre" value={editData.genre?.join(', ') || ''} onChange={handleInputChange} />
          </>
        )}
        {/* Educator specific fields can be added here similarly */}
        <div className="flex space-x-2 pt-4">
          <Button type="submit" variant="primary">Save Changes</Button>
          <Button type="button" variant="outline" onClick={() => { setIsEditing(false); setEditData(user); }}>Cancel</Button>
        </div>
      </form>
    </Card>
  );
  
  const artistTabs = [
    { name: 'My Portfolio', content: (
        <ProfileSection title="My Portfolio" actionButton={<Button onClick={() => setIsCreatePostModalOpen(true)} leftIcon={<Icon name="plusCircle" />}>New Post</Button>}>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mt-4">
                {userPosts.map(post => <FeedItem key={post.id} post={post} author={user} />)}
                {userPosts.length === 0 && <p className="text-neutral-dark">No posts yet. Add your work!</p>}
            </div>
        </ProfileSection>
    )},
    { name: 'Artist Statement', content: (
      <ProfileSection title="Artist Statement">
        <p className="text-neutral-dark whitespace-pre-wrap">{user.artistStatement || 'No artist statement provided.'}</p>
      </ProfileSection>
    )}
  ];

  const educatorTabs = [
     { name: 'My Events/Workshops', content: (
        <ProfileSection title="My Events/Workshops" actionButton={<Button onClick={() => setIsCreateEventModalOpen(true)} leftIcon={<Icon name="plusCircle" />}>New Event</Button>}>
            <div className="grid md:grid-cols-2 gap-6 mt-4">
                {userEvents.map(event => <EventItem key={event.id} event={event} educator={user} />)}
                {userEvents.length === 0 && <p className="text-neutral-dark">No events created yet.</p>}
            </div>
        </ProfileSection>
    )},
    { name: 'Lessons Offered', content: (
        <ProfileSection title="Lessons Offered" actionButton={<Button onClick={() => setIsCreateLessonModalOpen(true)} leftIcon={<Icon name="plusCircle" />}>New Lesson</Button>}>
            <div className="grid md:grid-cols-2 gap-6 mt-4">
                {userLessons.map(lesson => (
                  <Card key={lesson.id} className="p-4">
                    <div className="flex justify-between items-start mb-2">
                      <h4 className="font-semibold text-neutral-darkest">{lesson.title}</h4>
                      <div className="flex space-x-2">
                        <Button size="sm" variant="outline" onClick={() => setEditingLesson(lesson)}>
                          <Icon name="pencil" className="w-4 h-4" />
                        </Button>
                        <Button size="sm" variant="danger" onClick={() => handleDeleteLesson(lesson.id)}>
                          <Icon name="trash" className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                    <p className="text-neutral-dark text-sm mb-2">{lesson.description}</p>
                    <div className="flex justify-between text-xs text-neutral-dark">
                      <span>{lesson.duration || 'Duration not specified'}</span>
                      <span className="font-medium">{lesson.price || 'Price not specified'}</span>
                    </div>
                  </Card>
                ))}
                {userLessons.length === 0 && <p className="text-neutral-dark">No lessons created yet.</p>}
            </div>
        </ProfileSection>
    )}
  ];

  const fanTabs = [
    { name: 'Followed Artists', content: (
      <ProfileSection title="Followed Artists">
        { (user.followedArtistIds && user.followedArtistIds.length > 0) ?
          user.followedArtistIds.map(id => {
            const artist = getUserById(id);
            return artist ? <Link key={id} to={`/artist/${id}`} className="block p-2 hover:bg-neutral-light rounded">{artist.displayName}</Link> : null;
          }) : <p className="text-neutral-dark">You are not following any artists yet.</p>
        }
      </ProfileSection>
    )},
    { name: 'Saved Content', content: (
      <ProfileSection title="Saved Content">
          { (user.savedPostIds && user.savedPostIds.length > 0) ?
             getPosts().filter(p => user.savedPostIds?.includes(p.id)).map(post => (
                <FeedItem key={post.id} post={post} author={getUserById(post.userId) || undefined} />
             ))
             : <p className="text-neutral-dark">No saved content.</p>
          }
      </ProfileSection>
    )},
    { name: 'RSVP\'d Events', content: (
      <ProfileSection title="RSVP'd Events">
          { (user.rsvpedEventIds && user.rsvpedEventIds.length > 0) ?
             getEvents().filter(e => user.rsvpedEventIds?.includes(e.id)).map(event => (
                <EventItem key={event.id} event={event} educator={getUserById(event.educatorId) || undefined} />
             ))
             : <p className="text-neutral-dark">No events RSVP'd.</p>
          }
      </ProfileSection>
    )},
  ];
  
  let roleSpecificTabs: { name: string; content: React.ReactNode }[] = [];
  if (user.role === UserRole.ARTIST) roleSpecificTabs = artistTabs;
  else if (user.role === UserRole.EDUCATOR) roleSpecificTabs = educatorTabs;
  else if (user.role === UserRole.FAN) roleSpecificTabs = fanTabs;


  return (
    <PageContainer title="My Dashboard">
      <Banner src={user.bannerImageUrl} alt={`${user.displayName} banner`} />
      <div className="-mt-12 sm:-mt-16 md:-mt-20 relative z-10 px-4">
        {isEditing ? <ProfileEditForm /> : <ProfileDisplay />}
      </div>
      
      <div className="mt-8">
        <Tabs tabs={roleSpecificTabs} />
      </div>


      {/* Create Post Modal (for Artists) */}
      <Modal isOpen={isCreatePostModalOpen} onClose={() => setIsCreatePostModalOpen(false)} title="Create New Post">
        <form onSubmit={handleCreatePost} className="space-y-4">
          <Input label="Title" name="title" value={newPostData.title || ''} onChange={e => setNewPostData(p => ({...p, title: e.target.value}))} required />
          <Textarea label="Description" name="description" value={newPostData.description || ''} onChange={e => setNewPostData(p => ({...p, description: e.target.value}))} required />
          <Input label="Media URL (Image, YouTube, SoundCloud)" name="mediaUrl" value={newPostData.mediaUrl || ''} onChange={e => setNewPostData(p => ({...p, mediaUrl: e.target.value}))} required />
          <Select label="Media Type" name="mediaType" value={newPostData.mediaType || 'image'} onChange={e => setNewPostData(p => ({...p, mediaType: e.target.value as 'image' | 'youtube' | 'soundcloud'}))}
            options={[{value: 'image', label: 'Image'}, {value: 'youtube', label: 'YouTube Video'}, {value: 'soundcloud', label: 'SoundCloud Track'}]} />
          <Select label="Category" name="category" value={newPostData.category || PostCategory.VISUAL_ART} onChange={e => setNewPostData(p => ({...p, category: e.target.value as PostCategory}))}
            options={Object.values(PostCategory).map(c => ({value: c, label: c}))} />
          <Input label="Tags (comma-separated)" name="tags" value={newPostData.tags?.join(', ') || ''} onChange={e => setNewPostData(p => ({...p, tags: e.target.value.split(',').map(t=>t.trim())}))} />
          <Button type="submit" variant="primary">Create Post</Button>
        </form>
      </Modal>

      {/* Create Event Modal (for Educators) */}
      <Modal isOpen={isCreateEventModalOpen} onClose={() => setIsCreateEventModalOpen(false)} title="Create New Event/Workshop">
        <form onSubmit={handleCreateEvent} className="space-y-4">
            <Input label="Title" name="title" value={newEventData.title || ''} onChange={e => setNewEventData(p => ({...p, title: e.target.value}))} required />
            <Textarea label="Description" name="description" value={newEventData.description || ''} onChange={e => setNewEventData(p => ({...p, description: e.target.value}))} required />
            <Input label="Date & Time" type="datetime-local" name="dateTime" value={newEventData.dateTime || ''} onChange={e => setNewEventData(p => ({...p, dateTime: e.target.value}))} required />
            <Select label="Format" name="format" value={newEventData.format || 'virtual'} onChange={e => setNewEventData(p => ({...p, format: e.target.value as 'virtual' | 'in-person'}))}
                options={[{value: 'virtual', label: 'Virtual'}, {value: 'in-person', label: 'In-Person'}]} />
            <Input label="Location or Link" name="locationOrLink" value={newEventData.locationOrLink || ''} onChange={e => setNewEventData(p => ({...p, locationOrLink: e.target.value}))} required />
            <Input label="Price (optional, e.g., $20 or Free)" name="price" value={newEventData.price || ''} onChange={e => setNewEventData(p => ({...p, price: e.target.value}))} />
            <Button type="submit" variant="primary">Create Event</Button>
        </form>
      </Modal>

      {/* Create Lesson Modal (for Educators) */}
      <Modal isOpen={isCreateLessonModalOpen} onClose={() => setIsCreateLessonModalOpen(false)} title="Create New Lesson">
        <form onSubmit={handleCreateLesson} className="space-y-4">
            <Input label="Title" name="title" value={newLessonData.title || ''} onChange={e => setNewLessonData(p => ({...p, title: e.target.value}))} required />
            <Textarea label="Description" name="description" value={newLessonData.description || ''} onChange={e => setNewLessonData(p => ({...p, description: e.target.value}))} required />
            <Input label="Duration (e.g., '60 minutes')" name="duration" value={newLessonData.duration || ''} onChange={e => setNewLessonData(p => ({...p, duration: e.target.value}))} />
            <Input label="Price (e.g., '$30')" name="price" value={newLessonData.price || ''} onChange={e => setNewLessonData(p => ({...p, price: e.target.value}))} />
            <Button type="submit" variant="primary">Create Lesson</Button>
        </form>
      </Modal>

      {/* Edit Lesson Modal (for Educators) */}
      <Modal isOpen={!!editingLesson} onClose={() => setEditingLesson(null)} title="Edit Lesson">
        {editingLesson && (
          <form onSubmit={handleUpdateLesson} className="space-y-4">
              <Input label="Title" name="title" value={editingLesson.title || ''} onChange={e => setEditingLesson(p => p ? ({...p, title: e.target.value}) : null)} required />
              <Textarea label="Description" name="description" value={editingLesson.description || ''} onChange={e => setEditingLesson(p => p ? ({...p, description: e.target.value}) : null)} required />
              <Input label="Duration (e.g., '60 minutes')" name="duration" value={editingLesson.duration || ''} onChange={e => setEditingLesson(p => p ? ({...p, duration: e.target.value}) : null)} />
              <Input label="Price (e.g., '$30')" name="price" value={editingLesson.price || ''} onChange={e => setEditingLesson(p => p ? ({...p, price: e.target.value}) : null)} />
              <Button type="submit" variant="primary">Update Lesson</Button>
          </form>
        )}
      </Modal>

    </PageContainer>
  );
};

export default UserDashboardPage;
