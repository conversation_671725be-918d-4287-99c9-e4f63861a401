
import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { <PERSON><PERSON>, Card, Icon } from '../components';
import { PageContainer } from '../components/Layout';

const HomePage: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  return (
    <>
      {/* Animated Background Elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
        <div className="floating-notes">
          <div className="note note-1">♪</div>
          <div className="note note-2">♫</div>
          <div className="note note-3">♪</div>
          <div className="note note-4">♬</div>
          <div className="note note-5">♩</div>
          <div className="note note-6">♪</div>
        </div>
        <div className="paint-splashes">
          <div className="splash splash-1"></div>
          <div className="splash splash-2"></div>
          <div className="splash splash-3"></div>
        </div>
      </div>

      {/* Hero Section */}
      <div className="relative bg-gradient-to-br from-primary via-orange-500 to-secondary text-white py-32 px-4 text-center overflow-hidden">
        {/* Animated gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-blue-600/20 animate-gradient-shift"></div>

        {/* Floating geometric shapes */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="floating-shape shape-1"></div>
          <div className="floating-shape shape-2"></div>
          <div className="floating-shape shape-3"></div>
          <div className="floating-shape shape-4"></div>
        </div>

        <div className={`relative z-10 max-w-6xl mx-auto transform transition-all duration-1000 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
          <div className="mb-8 animate-bounce-slow">
            <div className="inline-block p-4 bg-white/10 rounded-full backdrop-blur-sm">
              <div className="text-6xl animate-pulse">🎨🎵</div>
            </div>
          </div>

          <h1 className="text-6xl md:text-8xl font-bold mb-6 leading-tight">
            <span className="inline-block animate-text-shimmer bg-gradient-to-r from-white via-accent to-white bg-clip-text text-transparent bg-300% animate-shimmer">
              You Love
            </span>
            <br />
            <span className="text-accent animate-pulse">MUSIC</span>
            <span className="mx-4 text-white">–</span>
            <span className="text-accent animate-pulse">ART</span>
          </h1>

          <p className="text-2xl md:text-3xl mb-12 max-w-4xl mx-auto leading-relaxed animate-fade-in-up">
            Join The MusicArt Club: A New Creative Community for Artists, Educators & Fans.
          </p>

          <div className="flex flex-col lg:flex-row justify-center items-center space-y-6 lg:space-y-0 lg:space-x-8 mb-8">
            <Button size="lg" className="bg-accent text-neutral-darkest hover:bg-accent-dark focus:ring-accent transform hover:scale-105 transition-all duration-300 shadow-2xl hover:shadow-accent/50 animate-bounce-in">
              <Link to="/auth?action=signup&role=Artist" className="flex items-center">
                <span className="mr-2">🎨</span>
                Join as an Artist
              </Link>
            </Button>
            <Button size="lg" className="bg-accent text-neutral-darkest hover:bg-accent-dark focus:ring-accent transform hover:scale-105 transition-all duration-300 shadow-2xl hover:shadow-accent/50 animate-bounce-in animation-delay-200">
              <Link to="/auth?action=signup&role=Educator" className="flex items-center">
                <span className="mr-2">🎓</span>
                Join as an Educator
              </Link>
            </Button>
          </div>

          <div className="flex flex-col sm:flex-row justify-center items-center space-y-4 sm:space-y-0 sm:space-x-6">
            <Button size="lg" variant="outline" className="border-2 border-white text-white hover:bg-white hover:text-primary-dark transform hover:scale-105 transition-all duration-300 backdrop-blur-sm bg-white/10 animate-bounce-in animation-delay-400">
              <Link to="/explore" className="flex items-center">
                <span className="mr-2">🔍</span>
                Browse the Feed
              </Link>
            </Button>
            <Button size="lg" variant="outline" className="border-2 border-white text-white hover:bg-white hover:text-primary-dark transform hover:scale-105 transition-all duration-300 backdrop-blur-sm bg-white/10 animate-bounce-in animation-delay-600">
              <Link to="/events" className="flex items-center">
                <span className="mr-2">📅</span>
                Discover Workshops
              </Link>
            </Button>
          </div>
        </div>
      </div>

      <PageContainer>
        {/* Featured Artists Section */}
        <section className="mt-20 relative bg-gradient-to-br from-orange-50/80 via-white/90 to-red-50/80 rounded-3xl shadow-lg backdrop-blur-sm border border-orange-100/50 py-16 px-6">
          <div className="text-center mb-12">
            <h2 className="text-4xl md:text-5xl font-bold text-neutral-darkest mb-4 animate-fade-in-up">
              <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                Featured Artists
              </span>
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-primary to-secondary mx-auto rounded-full animate-expand"></div>
            <p className="text-lg text-neutral-dark mt-4 max-w-2xl mx-auto">
              Discover the talented artists and musicians who make our community vibrant
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
            <Card className="p-6 text-center hover:shadow-2xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2 bg-white/90 backdrop-blur-sm border border-primary/30 animate-fade-in-up shadow-lg">
              <div className="w-20 h-20 bg-gradient-to-br from-primary to-secondary rounded-full mx-auto mb-4 flex items-center justify-center text-white text-2xl font-bold">
                🎨
              </div>
              <h3 className="text-xl font-bold mb-2 text-neutral-darkest">Visual Artists</h3>
              <p className="text-neutral-dark mb-4">Painters, digital artists, sculptors, and more showcasing their creative vision</p>
              <Button variant="outline" size="sm" className="border-primary text-primary hover:bg-primary hover:text-white">
                <Link to="/explore">View Gallery</Link>
              </Button>
            </Card>

            <Card className="p-6 text-center hover:shadow-2xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2 bg-white/90 backdrop-blur-sm border border-secondary/30 animate-fade-in-up animation-delay-200 shadow-lg">
              <div className="w-20 h-20 bg-gradient-to-br from-secondary to-accent rounded-full mx-auto mb-4 flex items-center justify-center text-white text-2xl font-bold">
                🎵
              </div>
              <h3 className="text-xl font-bold mb-2 text-neutral-darkest">Musicians</h3>
              <p className="text-neutral-dark mb-4">Composers, performers, producers sharing their musical journey and creations</p>
              <Button variant="outline" size="sm" className="border-secondary text-secondary hover:bg-secondary hover:text-white">
                <Link to="/explore">Listen Now</Link>
              </Button>
            </Card>

            <Card className="p-6 text-center hover:shadow-2xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2 bg-white/90 backdrop-blur-sm border border-accent/30 animate-fade-in-up animation-delay-400 shadow-lg">
              <div className="w-20 h-20 bg-gradient-to-br from-accent to-primary rounded-full mx-auto mb-4 flex items-center justify-center text-white text-2xl font-bold">
                🎭
              </div>
              <h3 className="text-xl font-bold mb-2 text-neutral-darkest">Performers</h3>
              <p className="text-neutral-dark mb-4">Dancers, actors, spoken word artists bringing stories to life</p>
              <Button variant="outline" size="sm" className="border-accent text-accent hover:bg-accent hover:text-white">
                <Link to="/explore">Watch Performances</Link>
              </Button>
            </Card>
          </div>

          <div className="text-center mt-12 animate-fade-in-up animation-delay-600">
            <Button variant="primary" size="lg" className="transform hover:scale-105 transition-all duration-300 shadow-xl hover:shadow-primary/50">
              <Link to="/explore" className="flex items-center">
                <span className="mr-2">🌟</span>
                Join Our Creative Community
              </Link>
            </Button>
          </div>
        </section>

        {/* Community Stats & Achievements Section */}
        <section className="mt-24 relative bg-gradient-to-br from-red-50/80 via-purple-50/60 to-blue-50/80 rounded-3xl shadow-lg backdrop-blur-sm border border-red-100/50 py-16 px-6">
          <div className="text-center mb-12">
            <h2 className="text-4xl md:text-5xl font-bold text-neutral-darkest mb-4 animate-fade-in-up">
              <span className="bg-gradient-to-r from-secondary to-primary bg-clip-text text-transparent">
                Our Creative Community
              </span>
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-secondary to-primary mx-auto rounded-full animate-expand"></div>
            <p className="text-lg text-neutral-dark mt-4 max-w-2xl mx-auto">
              Join thousands of artists, musicians, and creative minds sharing their passion
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-6xl mx-auto mb-16">
            <div className="text-center p-6 bg-white/80 backdrop-blur-sm rounded-2xl border border-primary/30 animate-fade-in-up shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="text-4xl md:text-5xl font-bold text-primary mb-2">2.5K+</div>
              <div className="text-neutral-dark font-medium">Active Artists</div>
            </div>
            <div className="text-center p-6 bg-white/80 backdrop-blur-sm rounded-2xl border border-secondary/30 animate-fade-in-up animation-delay-100 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="text-4xl md:text-5xl font-bold text-secondary mb-2">15K+</div>
              <div className="text-neutral-dark font-medium">Artworks Shared</div>
            </div>
            <div className="text-center p-6 bg-white/80 backdrop-blur-sm rounded-2xl border border-accent/30 animate-fade-in-up animation-delay-200 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="text-4xl md:text-5xl font-bold text-accent mb-2">500+</div>
              <div className="text-neutral-dark font-medium">Workshops Held</div>
            </div>
            <div className="text-center p-6 bg-white/80 backdrop-blur-sm rounded-2xl border border-primary/30 animate-fade-in-up animation-delay-300 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent mb-2">50+</div>
              <div className="text-neutral-dark font-medium">Countries</div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-6xl mx-auto">
            <Card className="p-8 bg-white/90 backdrop-blur-sm border border-primary/30 hover:shadow-2xl transition-all duration-500 animate-fade-in-up animation-delay-400 shadow-lg">
              <div className="flex items-start space-x-4">
                <div className="w-12 h-12 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center text-white text-xl flex-shrink-0">
                  💬
                </div>
                <div>
                  <h3 className="text-xl font-bold text-neutral-darkest mb-2">Member Testimonial</h3>
                  <p className="text-neutral-dark italic mb-3">"MusicArt Club has transformed my creative journey. The supportive community and amazing workshops have helped me grow as both an artist and musician."</p>
                  <p className="text-sm font-medium text-primary">- Sarah Chen, Digital Artist & Composer</p>
                </div>
              </div>
            </Card>

            <Card className="p-8 bg-white/90 backdrop-blur-sm border border-secondary/30 hover:shadow-2xl transition-all duration-500 animate-fade-in-up animation-delay-500 shadow-lg">
              <div className="flex items-start space-x-4">
                <div className="w-12 h-12 bg-gradient-to-br from-secondary to-accent rounded-full flex items-center justify-center text-white text-xl flex-shrink-0">
                  🏆
                </div>
                <div>
                  <h3 className="text-xl font-bold text-neutral-darkest mb-2">Recent Achievement</h3>
                  <p className="text-neutral-dark mb-3">Our community member Alex Rodriguez just won the International Digital Art Competition! Their journey started right here in our club.</p>
                  <p className="text-sm font-medium text-secondary">Celebrating creative excellence since 2020</p>
                </div>
              </div>
            </Card>
          </div>

          <div className="text-center mt-12 animate-fade-in-up animation-delay-600">
            <Button variant="primary" size="lg" className="transform hover:scale-105 transition-all duration-300 shadow-xl hover:shadow-primary/50">
              <Link to="/events" className="flex items-center">
                <span className="mr-2">🎓</span>
                Explore Workshops & Events
              </Link>
            </Button>
          </div>
        </section>

        {/* Why Join Section */}
        <section className="mt-24 py-20 bg-gradient-to-br from-yellow-50/80 via-orange-50/60 to-amber-50/80 rounded-3xl shadow-2xl relative overflow-hidden border border-yellow-100/50 backdrop-blur-sm">
          {/* Background decoration */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute top-10 left-10 text-8xl">🎵</div>
            <div className="absolute top-20 right-20 text-6xl">🎨</div>
            <div className="absolute bottom-10 left-20 text-7xl">🎭</div>
            <div className="absolute bottom-20 right-10 text-5xl">🎪</div>
          </div>

          <div className="relative z-10">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold text-neutral-darkest mb-4 animate-fade-in-up">
                <span className="bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent">
                  Why Join MusicArt Club?
                </span>
              </h2>
              <div className="w-32 h-1 bg-gradient-to-r from-primary via-secondary to-accent mx-auto rounded-full animate-expand"></div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-10 max-w-6xl mx-auto px-6">
              <Card className="p-8 text-center hover:shadow-2xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2 bg-white/80 backdrop-blur-sm border border-primary/20 animate-fade-in-up">
                <div className="mb-6 transform hover:scale-110 transition-transform duration-300">
                  <Icon name="users" className="w-16 h-16 text-primary mx-auto animate-pulse" />
                </div>
                <h3 className="text-2xl font-bold mb-4 text-neutral-darkest">Connect & Collaborate</h3>
                <p className="text-neutral-dark leading-relaxed">Meet fellow artists, educators, and fans. Find your next collaborator or student in our vibrant community.</p>
              </Card>

              <Card className="p-8 text-center hover:shadow-2xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2 bg-white/80 backdrop-blur-sm border border-secondary/20 animate-fade-in-up animation-delay-200">
                <div className="mb-6 transform hover:scale-110 transition-transform duration-300">
                  <Icon name="photo" className="w-16 h-16 text-secondary mx-auto animate-pulse" />
                </div>
                <h3 className="text-2xl font-bold mb-4 text-neutral-darkest">Showcase Your Talent</h3>
                <p className="text-neutral-dark leading-relaxed">Share your music, visual art, and performances with a supportive community that celebrates creativity.</p>
              </Card>

              <Card className="p-8 text-center hover:shadow-2xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2 bg-white/80 backdrop-blur-sm border border-accent/20 animate-fade-in-up animation-delay-400">
                <div className="mb-6 transform hover:scale-110 transition-transform duration-300">
                  <Icon name="academicCap" className="w-16 h-16 text-accent-dark mx-auto animate-pulse" />
                </div>
                <h3 className="text-2xl font-bold mb-4 text-neutral-darkest">Learn & Grow</h3>
                <p className="text-neutral-dark leading-relaxed">Discover workshops, lessons, and resources to expand your creative skills and artistic journey.</p>
              </Card>
            </div>
          </div>
        </section>
      </PageContainer>
    </>
  );
};

export default HomePage;
