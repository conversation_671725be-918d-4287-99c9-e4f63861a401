rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    function getUserData() {
      return firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data;
    }
    
    function isAdmin() {
      return isAuthenticated() && getUserData().role == 'Admin';
    }
    
    function isValidImageFile() {
      return request.resource.contentType.matches('image/.*') &&
             request.resource.size < 10 * 1024 * 1024; // 10MB limit
    }
    
    function isValidMediaFile() {
      return (request.resource.contentType.matches('image/.*') ||
              request.resource.contentType.matches('audio/.*') ||
              request.resource.contentType.matches('video/.*')) &&
             request.resource.size < 50 * 1024 * 1024; // 50MB limit
    }
    
    // Profile photos - users can upload their own, admins can manage all
    match /profile-photos/{userId}/{fileName} {
      allow read: if true; // Public read access
      allow write: if isAuthenticated() && (isOwner(userId) || isAdmin()) && isValidImageFile();
      allow delete: if isAuthenticated() && (isOwner(userId) || isAdmin());
    }
    
    // Banner images - users can upload their own, admins can manage all
    match /banner-images/{userId}/{fileName} {
      allow read: if true; // Public read access
      allow write: if isAuthenticated() && (isOwner(userId) || isAdmin()) && isValidImageFile();
      allow delete: if isAuthenticated() && (isOwner(userId) || isAdmin());
    }
    
    // Post media - artists can upload their own, admins can manage all
    match /post-media/{userId}/{fileName} {
      allow read: if true; // Public read access
      allow write: if isAuthenticated() && (isOwner(userId) || isAdmin()) && isValidMediaFile();
      allow delete: if isAuthenticated() && (isOwner(userId) || isAdmin());
    }
    
    // General uploads - authenticated users can upload, admins can manage all
    match /uploads/{userId}/{fileName} {
      allow read: if true; // Public read access
      allow write: if isAuthenticated() && (isOwner(userId) || isAdmin()) && isValidMediaFile();
      allow delete: if isAuthenticated() && (isOwner(userId) || isAdmin());
    }
    
    // Admin-only files
    match /admin/{fileName} {
      allow read, write, delete: if isAdmin();
    }
    
    // Deny all other access
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
