# Firebase Setup Guide for MusicArt Club

This guide will help you set up Firebase for your MusicArt Club application with full functionality for all user types.

## 🔥 Firebase Project Configuration

Your Firebase project details:
- **Project ID**: `musicart-7641d`
- **Storage Bucket**: `gs://musicart-7641d.firebasestorage.app`
- **Hosting URL**: `https://musicart-7641d.web.app`
- **Console**: `https://console.firebase.google.com/project/musicart-7641d`

## 📋 Prerequisites

1. **Node.js** (v16 or higher)
2. **npm** or **yarn**
3. **Firebase CLI** (will be installed automatically)

## 🚀 Quick Setup (Automated)

### Option 1: Using the Setup Script (Recommended)

**For Unix/Linux/macOS:**
```bash
chmod +x scripts/firebase-setup.sh
./scripts/firebase-setup.sh
```

**For Windows (PowerShell):**
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
.\scripts\firebase-setup.ps1
```

### Option 2: Manual Setup

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Install Firebase CLI globally:**
   ```bash
   npm install -g firebase-tools
   ```

3. **Login to Firebase:**
   ```bash
   firebase login
   ```

4. **Initialize Firebase project:**
   ```bash
   firebase init
   ```
   Select:
   - ✅ Firestore
   - ✅ Hosting
   - ✅ Storage
   - ✅ Emulators (optional, for development)

5. **Deploy rules and indexes:**
   ```bash
   firebase deploy --only firestore:rules,firestore:indexes,storage
   ```

6. **Build and deploy:**
   ```bash
   npm run build
   firebase deploy --only hosting
   ```

## 🗄️ Database Structure

### Collections Created:

1. **users** - User profiles and authentication data
2. **posts** - Artist posts and media content
3. **events** - Educator events and workshops
4. **lessons** - Educator lesson offerings
5. **messages** - Direct messages between users
6. **comments** - Comments on posts
7. **siteSettings** - Global application settings
8. **announcements** - Admin announcements

### Indexes Created:

- Posts by category and creation date
- Posts by user and creation date
- Events by educator and date
- Messages by sender/receiver and date
- Comments by post and date
- Users by role and activity status

## 🔐 Security Rules

### Firestore Rules:
- **Users**: Read access for all authenticated users, write access for owners and admins
- **Posts**: Public read, artist-only create, owner/admin edit/delete
- **Events**: Public read, educator-only create, owner/admin edit/delete
- **Messages**: Private read/write for sender/receiver, admin oversight
- **Comments**: Public read, authenticated create, owner/admin delete
- **Admin Collections**: Admin-only access

### Storage Rules:
- **Profile Photos**: Public read, owner/admin write (10MB limit)
- **Banner Images**: Public read, owner/admin write (10MB limit)
- **Post Media**: Public read, owner/admin write (50MB limit)

## 👥 User Roles & Permissions

### 🎨 Artist
- Create and manage posts
- Upload media content
- Receive messages from fans
- Update profile and portfolio

### 🎓 Educator
- Create and manage events
- Offer lessons and workshops
- Communicate with students
- Share educational content

### ❤️ Fan
- Follow artists
- Save posts
- RSVP to events
- Send messages to artists/educators

### 👑 Admin
- Full system access
- User management
- Content moderation
- Site configuration
- Analytics and reporting

## 🔧 Environment Configuration

Create a `.env.local` file with your Firebase configuration:

```env
VITE_FIREBASE_API_KEY=AIzaSyBhVCh-UFBHIYdnLQ0C37GHFSCGpmQOHc8
VITE_FIREBASE_AUTH_DOMAIN=musicart-7641d.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=musicart-7641d
VITE_FIREBASE_STORAGE_BUCKET=musicart-7641d.firebasestorage.app
VITE_FIREBASE_MESSAGING_SENDER_ID=732159852081
VITE_FIREBASE_APP_ID=1:732159852081:web:4117b804da97bb8bc55f10
VITE_FIREBASE_MEASUREMENT_ID=G-38F2VTX3SN
```

## 🧪 Development with Emulators

To run with Firebase emulators for development:

```bash
firebase emulators:start
npm run dev
```

This will start:
- Firestore Emulator (port 8080)
- Authentication Emulator (port 9099)
- Storage Emulator (port 9199)
- Emulator UI (port 4000)

## 📊 Data Migration

The application automatically migrates existing localStorage data to Firebase on first run. The migration includes:

- User profiles and preferences
- Posts and media content
- Events and RSVPs
- Messages and comments
- Site settings and announcements

## 🚀 Deployment Commands

```bash
# Deploy everything
npm run firebase:deploy

# Deploy only rules
npm run firebase:rules

# Deploy only indexes
npm run firebase:indexes

# Start emulators
npm run firebase:emulators
```

## 🔍 Monitoring & Analytics

1. **Firebase Console**: Monitor usage, performance, and errors
2. **Google Analytics**: Track user engagement and behavior
3. **Performance Monitoring**: Identify bottlenecks and optimize
4. **Crashlytics**: Monitor and fix application crashes

## 🛠️ Troubleshooting

### Common Issues:

1. **Permission Denied**: Check Firestore security rules
2. **Index Missing**: Deploy indexes with `firebase deploy --only firestore:indexes`
3. **Storage Upload Fails**: Verify storage rules and file size limits
4. **Authentication Issues**: Check Firebase Auth configuration

### Debug Mode:

Enable debug logging:
```javascript
// In firebase.ts
import { connectFirestoreEmulator } from 'firebase/firestore';
// Add debug logging
```

## 📞 Support

For issues or questions:
1. Check Firebase Console for errors
2. Review security rules and indexes
3. Check browser console for client-side errors
4. Verify environment variables are set correctly

## 🎯 Next Steps

After setup:
1. Configure authentication providers (Google, Facebook, etc.)
2. Set up custom domain
3. Configure backup schedules
4. Set up monitoring alerts
5. Optimize performance based on usage patterns
