import React, { useState } from 'react';
import { useFirebaseApp } from '../FirebaseAppContext';
import { SiteSettings } from '../types';
import { Button, Input, Card, Icon } from '../components';

interface AdminSiteSettingsProps {
  className?: string;
}

export const AdminSiteSettings: React.FC<AdminSiteSettingsProps> = ({ className = '' }) => {
  const { getSiteSettings, updateSiteSettings } = useFirebaseApp();
  const currentSettings = getSiteSettings();

  const [settings, setSettings] = useState<SiteSettings>(currentSettings);
  const [isLoading, setIsLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  const handleSave = async () => {
    setIsLoading(true);
    setSuccessMessage('');

    try {
      const success = await updateSiteSettings(settings);
      if (success) {
        setSuccessMessage('Settings saved successfully!');
        setTimeout(() => setSuccessMessage(''), 3000);
      }
    } catch (error) {
      console.error('Failed to save settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: keyof SiteSettings, value: any) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleThemeChange = (field: keyof SiteSettings['theme'], value: string) => {
    setSettings(prev => ({
      ...prev,
      theme: {
        ...prev.theme,
        [field]: value
      }
    }));
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Success Message */}
      {successMessage && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 flex items-center">
          <Icon name="checkCircle" className="w-5 h-5 text-green-500 mr-2" />
          <span className="text-green-800">{successMessage}</span>
        </div>
      )}

      {/* General Settings */}
      <Card className="p-6">
        <h3 className="text-xl font-semibold text-neutral-darkest mb-4 flex items-center">
          <Icon name="cog6Tooth" className="w-6 h-6 mr-2" />
          General Settings
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Input
            label="Site Name"
            value={settings.siteName}
            onChange={(e) => handleInputChange('siteName', e.target.value)}
            placeholder="The MusicArt Club"
          />
          <Input
            label="Contact Email"
            type="email"
            value={settings.contactEmail}
            onChange={(e) => handleInputChange('contactEmail', e.target.value)}
            placeholder="<EMAIL>"
          />
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-neutral-dark mb-1">
              Site Description
            </label>
            <textarea
              className="block w-full px-3 py-2 border border-neutral-light rounded-md focus:outline-none focus:ring-primary focus:border-primary"
              rows={3}
              value={settings.siteDescription}
              onChange={(e) => handleInputChange('siteDescription', e.target.value)}
              placeholder="A creative community for music and art enthusiasts..."
            />
          </div>
        </div>
      </Card>

      {/* Feature Toggles */}
      <Card className="p-6">
        <h3 className="text-xl font-semibold text-neutral-darkest mb-4 flex items-center">
          <Icon name="squares2x2" className="w-6 h-6 mr-2" />
          Feature Settings
        </h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-neutral-darkest">Maintenance Mode</h4>
              <p className="text-sm text-neutral-dark">Temporarily disable site access for maintenance</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.maintenanceMode}
                onChange={(e) => handleInputChange('maintenanceMode', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
            </label>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-neutral-darkest">Allow Registration</h4>
              <p className="text-sm text-neutral-dark">Allow new users to register accounts</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.allowRegistration}
                onChange={(e) => handleInputChange('allowRegistration', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
            </label>
          </div>

          <div>
            <label className="block text-sm font-medium text-neutral-dark mb-1">
              Featured Posts Limit
            </label>
            <Input
              type="number"
              min="1"
              max="20"
              value={settings.featuredPostsLimit}
              onChange={(e) => handleInputChange('featuredPostsLimit', parseInt(e.target.value) || 5)}
              placeholder="5"
            />
          </div>
        </div>
      </Card>

      {/* Theme Settings */}
      <Card className="p-6">
        <h3 className="text-xl font-semibold text-neutral-darkest mb-4 flex items-center">
          <Icon name="paintBrush" className="w-6 h-6 mr-2" />
          Theme Customization
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-medium text-neutral-dark mb-1">
              Primary Color
            </label>
            <div className="flex items-center space-x-2">
              <input
                type="color"
                value={settings.theme.primaryColor}
                onChange={(e) => handleThemeChange('primaryColor', e.target.value)}
                className="w-12 h-10 border border-neutral-light rounded cursor-pointer"
              />
              <Input
                value={settings.theme.primaryColor}
                onChange={(e) => handleThemeChange('primaryColor', e.target.value)}
                placeholder="#f97316"
                className="flex-1"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-neutral-dark mb-1">
              Secondary Color
            </label>
            <div className="flex items-center space-x-2">
              <input
                type="color"
                value={settings.theme.secondaryColor}
                onChange={(e) => handleThemeChange('secondaryColor', e.target.value)}
                className="w-12 h-10 border border-neutral-light rounded cursor-pointer"
              />
              <Input
                value={settings.theme.secondaryColor}
                onChange={(e) => handleThemeChange('secondaryColor', e.target.value)}
                placeholder="#8b5cf6"
                className="flex-1"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-neutral-dark mb-1">
              Accent Color
            </label>
            <div className="flex items-center space-x-2">
              <input
                type="color"
                value={settings.theme.accentColor}
                onChange={(e) => handleThemeChange('accentColor', e.target.value)}
                className="w-12 h-10 border border-neutral-light rounded cursor-pointer"
              />
              <Input
                value={settings.theme.accentColor}
                onChange={(e) => handleThemeChange('accentColor', e.target.value)}
                placeholder="#06b6d4"
                className="flex-1"
              />
            </div>
          </div>
        </div>

        {/* Color Preview */}
        <div className="mt-6 p-4 border border-neutral-light rounded-lg">
          <h4 className="text-sm font-medium text-neutral-dark mb-3">Color Preview</h4>
          <div className="flex space-x-4">
            <div
              className="w-16 h-16 rounded-lg shadow-md flex items-center justify-center text-white text-xs font-medium"
              style={{ backgroundColor: settings.theme.primaryColor }}
            >
              Primary
            </div>
            <div
              className="w-16 h-16 rounded-lg shadow-md flex items-center justify-center text-white text-xs font-medium"
              style={{ backgroundColor: settings.theme.secondaryColor }}
            >
              Secondary
            </div>
            <div
              className="w-16 h-16 rounded-lg shadow-md flex items-center justify-center text-white text-xs font-medium"
              style={{ backgroundColor: settings.theme.accentColor }}
            >
              Accent
            </div>
          </div>
        </div>
      </Card>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button
          onClick={handleSave}
          isLoading={isLoading}
          className="px-8"
        >
          <Icon name="cloudArrowUp" className="w-5 h-5 mr-2" />
          Save Settings
        </Button>
      </div>
    </div>
  );
};