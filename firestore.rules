rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    function getUserData() {
      return exists(/databases/$(database)/documents/users/$(request.auth.uid)) ?
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data :
        null;
    }

    function isAdmin() {
      return isAuthenticated() && getUserData() != null && getUserData().role == 'Admin';
    }
    
    function isArtist() {
      return isAuthenticated() && getUserData().role == 'Artist';
    }
    
    function isEducator() {
      return isAuthenticated() && getUserData().role == 'Educator';
    }
    
    function isFan() {
      return isAuthenticated() && getUserData().role == 'Fan';
    }
    
    function isActiveUser() {
      return isAuthenticated() && getUserData().isActive == true;
    }
    
    // Users collection
    match /users/{userId} {
      // Allow read access to all authenticated users
      allow read: if isAuthenticated();

      // Allow users to create their own profile, or during initial setup
      allow create: if isAuthenticated() && (isOwner(userId) || getUserData() == null);

      // Allow users to update their own profile, or admins to update any profile
      allow update: if isAuthenticated() && (isOwner(userId) || isAdmin());

      // Only admins can delete users
      allow delete: if isAdmin();
    }
    
    // Posts collection
    match /posts/{postId} {
      // Allow read access to all users (including unauthenticated for public viewing)
      allow read: if true;
      
      // Only artists can create posts
      allow create: if isAuthenticated() && isArtist() && isActiveUser();
      
      // Allow post owner or admin to update posts
      allow update: if isAuthenticated() && (
        (isArtist() && resource.data.userId == request.auth.uid) || 
        isAdmin()
      );
      
      // Allow post owner or admin to delete posts
      allow delete: if isAuthenticated() && (
        (isArtist() && resource.data.userId == request.auth.uid) || 
        isAdmin()
      );
    }
    
    // Events collection
    match /events/{eventId} {
      // Allow read access to all users
      allow read: if true;
      
      // Only educators can create events
      allow create: if isAuthenticated() && isEducator() && isActiveUser();
      
      // Allow event owner or admin to update events
      allow update: if isAuthenticated() && (
        (isEducator() && resource.data.educatorId == request.auth.uid) || 
        isAdmin()
      );
      
      // Allow event owner or admin to delete events
      allow delete: if isAuthenticated() && (
        (isEducator() && resource.data.educatorId == request.auth.uid) || 
        isAdmin()
      );
    }
    
    // Lessons collection
    match /lessons/{lessonId} {
      // Allow read access to all users
      allow read: if true;
      
      // Only educators can create lessons
      allow create: if isAuthenticated() && isEducator() && isActiveUser();
      
      // Allow lesson owner or admin to update lessons
      allow update: if isAuthenticated() && (
        (isEducator() && resource.data.educatorId == request.auth.uid) || 
        isAdmin()
      );
      
      // Allow lesson owner or admin to delete lessons
      allow delete: if isAuthenticated() && (
        (isEducator() && resource.data.educatorId == request.auth.uid) || 
        isAdmin()
      );
    }
    
    // Messages collection
    match /messages/{messageId} {
      // Allow read access to sender and receiver
      allow read: if isAuthenticated() && (
        resource.data.senderId == request.auth.uid || 
        resource.data.receiverId == request.auth.uid ||
        isAdmin()
      );
      
      // Allow authenticated users to send messages
      allow create: if isAuthenticated() && isActiveUser() && 
        request.resource.data.senderId == request.auth.uid;
      
      // Allow sender, receiver, or admin to update messages (for read status)
      allow update: if isAuthenticated() && (
        resource.data.senderId == request.auth.uid || 
        resource.data.receiverId == request.auth.uid ||
        isAdmin()
      );
      
      // Allow sender, receiver, or admin to delete messages
      allow delete: if isAuthenticated() && (
        resource.data.senderId == request.auth.uid || 
        resource.data.receiverId == request.auth.uid ||
        isAdmin()
      );
    }
    
    // Comments collection
    match /comments/{commentId} {
      // Allow read access to all users
      allow read: if true;
      
      // Allow authenticated users to create comments
      allow create: if isAuthenticated() && isActiveUser() && 
        request.resource.data.userId == request.auth.uid;
      
      // Allow comment owner or admin to update comments
      allow update: if isAuthenticated() && (
        resource.data.userId == request.auth.uid || 
        isAdmin()
      );
      
      // Allow comment owner or admin to delete comments
      allow delete: if isAuthenticated() && (
        resource.data.userId == request.auth.uid || 
        isAdmin()
      );
    }
    
    // Site settings collection
    match /siteSettings/{settingId} {
      // Allow read access to all users
      allow read: if true;

      // Allow creation during initial setup or by admins
      allow create: if isAuthenticated() && (getUserData() == null || isAdmin());

      // Only admins can update or delete site settings
      allow update, delete: if isAdmin();
    }

    // Announcements collection
    match /announcements/{announcementId} {
      // Allow read access to all users
      allow read: if true;

      // Allow creation during initial setup or by admins
      allow create: if isAuthenticated() && (getUserData() == null || isAdmin());

      // Only admins can update or delete announcements
      allow update, delete: if isAdmin();
    }
  }
}
