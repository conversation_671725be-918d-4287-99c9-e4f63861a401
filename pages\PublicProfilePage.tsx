
import React, { useEffect, useState } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { PageContainer } from '../components/Layout';
import { useFirebaseApp } from '../FirebaseAppContext';
import { User, UserRole, Post, Event } from '../types';
import { Avatar, Banner, Card, Tag, FeedItem, EventItem, ProfileSection, Button, Icon, SocialLinkItem, Tabs } from '../components';

const PublicProfilePage: React.FC = () => {
  const { userId } = useParams<{ userId: string }>();
  const { getUserById, getPosts, getEvents, currentUser, followArtist } = useFirebaseApp();
  const [profileUser, setProfileUser] = useState<User | null>(null);
  const [userContent, setUserContent] = useState<{ posts: Post[]; events: Event[] }>({ posts: [], events: [] });
  const [isFollowing, setIsFollowing] = useState(false);

  useEffect(() => {
    if (userId) {
      const fetchedUser = getUserById(userId);
      setProfileUser(fetchedUser);
      if (fetchedUser) {
        if (fetchedUser.role === UserRole.ARTIST) {
          setUserContent(prev => ({ ...prev, posts: getPosts({ userId: fetchedUser.id }) }));
        }
        if (fetchedUser.role === UserRole.EDUCATOR) {
          setUserContent(prev => ({ ...prev, events: getEvents({ educatorId: fetchedUser.id }) }));
        }
        if (currentUser && currentUser.role === UserRole.FAN && currentUser.followedArtistIds?.includes(userId)) {
            setIsFollowing(true);
        } else {
            setIsFollowing(false);
        }
      }
    }
  }, [userId, getUserById, getPosts, getEvents, currentUser]);

  const handleFollow = async () => {
    if (!currentUser || !profileUser || currentUser.id === profileUser.id) return;
    const success = await followArtist(currentUser.id, profileUser.id);
    if (success) {
        setIsFollowing(!isFollowing);
    }
  };


  if (!profileUser) {
    return <PageContainer title="Profile Not Found"><p>The user profile could not be found.</p></PageContainer>;
  }

  const artistTabs = [
    { name: 'Portfolio', content: (
        <ProfileSection title={`${profileUser.displayName}'s Portfolio`}>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mt-4">
            {userContent.posts.map(post => <FeedItem key={post.id} post={post} author={profileUser} />)}
            {userContent.posts.length === 0 && <p className="text-neutral-dark">This artist hasn't posted any work yet.</p>}
          </div>
        </ProfileSection>
    )},
    { name: 'Artist Statement', content: (
      <ProfileSection title="Artist Statement">
        <p className="text-neutral-dark whitespace-pre-wrap">{profileUser.artistStatement || 'No artist statement provided.'}</p>
      </ProfileSection>
    )}
  ];

  const educatorTabs = [
     { name: 'Events & Workshops', content: (
        <ProfileSection title={`${profileUser.displayName}'s Events & Workshops`}>
          <div className="grid md:grid-cols-2 gap-6 mt-4">
            {userContent.events.map(event => <EventItem key={event.id} event={event} educator={profileUser} />)}
            {userContent.events.length === 0 && <p className="text-neutral-dark">This educator has no upcoming events.</p>}
          </div>
        </ProfileSection>
    )},
    { name: 'About', content: ( // Generic about for educators
        <ProfileSection title={`About ${profileUser.displayName}`}>
          <p className="text-neutral-dark">{profileUser.bio || 'No detailed bio provided.'}</p>
          {/* List lessons if available */}
        </ProfileSection>
    )}
  ];
  
  let roleSpecificTabs: { name: string; content: React.ReactNode }[] = [];
  if (profileUser.role === UserRole.ARTIST) roleSpecificTabs = artistTabs;
  else if (profileUser.role === UserRole.EDUCATOR) roleSpecificTabs = educatorTabs;
  else { // For Fans, or general users if that becomes a thing
      roleSpecificTabs = [{ name: 'About', content: (
          <ProfileSection title={`About ${profileUser.displayName}`}>
             <p className="text-neutral-dark">{profileUser.bio || 'No bio provided.'}</p>
          </ProfileSection>
      )}];
  }


  return (
    <PageContainer title={`${profileUser.displayName}'s Profile`}>
      <Banner src={profileUser.bannerImageUrl} alt={`${profileUser.displayName} banner`} />
      <div className="-mt-12 sm:-mt-16 md:-mt-20 relative z-10 px-4">
        <Card className="p-6">
          <div className="flex flex-col sm:flex-row items-center sm:items-start">
            <Avatar src={profileUser.profilePhotoUrl} alt={profileUser.displayName} size="lg" className="mb-4 sm:mb-0 sm:mr-6" />
            <div className="text-center sm:text-left flex-grow">
              <h2 className="text-3xl font-bold text-neutral-darkest">{profileUser.displayName}</h2>
              <p className="text-md text-primary">{profileUser.role}</p>
              {profileUser.location && <p className="text-sm text-neutral-dark mt-1"><Icon name="mapPin" className="inline w-4 h-4 mr-1" />{profileUser.location}</p>}
              <div className="mt-2">
                {profileUser.tags.map(tag => <Tag key={tag}>{tag}</Tag>)}
              </div>
            </div>
            {currentUser && currentUser.id !== profileUser.id && (profileUser.role === UserRole.ARTIST || profileUser.role === UserRole.EDUCATOR) && (
              <Button onClick={handleFollow} variant={isFollowing ? "outline" : "primary"} className="mt-4 sm:mt-0">
                {isFollowing ? 'Unfollow' : `Follow ${profileUser.role}`}
              </Button>
            )}
          </div>
          {profileUser.bio && <p className="mt-4 text-neutral-dark text-center sm:text-left">{profileUser.bio}</p>}
          <div className="mt-4 flex justify-center sm:justify-start">
            <SocialLinkItem platform="Instagram" url={profileUser.socialLinks?.instagram} iconName="link" />
            <SocialLinkItem platform="TikTok" url={profileUser.socialLinks?.tiktok} iconName="link" />
            <SocialLinkItem platform="YouTube" url={profileUser.socialLinks?.youtube} iconName="link" />
            <SocialLinkItem platform="SoundCloud" url={profileUser.socialLinks?.soundcloud} iconName="link" />
            <SocialLinkItem platform="Spotify" url={profileUser.socialLinks?.spotify} iconName="link" />
          </div>
        </Card>
      </div>

      <div className="mt-8">
        <Tabs tabs={roleSpecificTabs} />
      </div>
      
      {/* Basic contact option placeholder */}
      {profileUser.id !== currentUser?.id && (
        <div className="mt-8 text-center">
            <Button variant="secondary">
                Contact {profileUser.displayName} {/* This would trigger a modal or form */}
            </Button>
        </div>
      )}

    </PageContainer>
  );
};

export default PublicProfilePage;

