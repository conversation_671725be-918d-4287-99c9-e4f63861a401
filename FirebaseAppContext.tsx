// Firebase-enabled AppContext to replace localStorage-based context
import React, { createContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { User as FirebaseUser } from 'firebase/auth';
import { User, Post, Event, Lesson, Message, Comment, UserRole, AppContextType, PostCategory, SiteSettings, SystemHealth, Announcement, ContentStats } from './types';
import { 
  AuthService, 
  UserService, 
  PostService, 
  EventService, 
  LessonService, 
  MessageService, 
  CommentService, 
  SiteSettingsService, 
  AnnouncementService 
} from './services/firebaseService';
import { DataMigrationService } from './services/dataMigration';

export const FirebaseAppContext = createContext<AppContextType | undefined>(undefined);

export const FirebaseAppProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [posts, setPosts] = useState<Post[]>([]);
  const [events, setEvents] = useState<Event[]>([]);
  const [lessons, setLessons] = useState<Lesson[]>([]);
  const [messages, setMessages] = useState<Message[]>([]);
  const [comments, setComments] = useState<Comment[]>([]);
  const [siteSettings, setSiteSettings] = useState<SiteSettings | null>(null);
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize Firebase and handle data migration
  useEffect(() => {
    const initializeApp = async () => {
      try {
        setIsLoading(true);
        
        // Check if migration is needed
        if (DataMigrationService.isMigrationNeeded()) {
          console.log('Migration needed, starting migration...');
          const migrationResult = await DataMigrationService.migrateAllData();
          
          if (migrationResult.success) {
            console.log('Migration completed successfully');
            DataMigrationService.clearLocalStorageData();
          } else {
            console.error('Migration failed:', migrationResult.errors);
          }
        }
        
        // Initialize default data if needed
        await DataMigrationService.initializeDefaultData();
        
        // Load initial data
        await loadAllData();
        
        setIsInitialized(true);
      } catch (error) {
        console.error('Error initializing app:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    initializeApp();
  }, []);

  // Set up auth state listener
  useEffect(() => {
    const unsubscribe = AuthService.onAuthStateChanged(async (firebaseUser: FirebaseUser | null) => {
      if (firebaseUser) {
        // Get user data from Firestore
        const userData = await UserService.getUserById(firebaseUser.uid);
        setCurrentUser(userData);
      } else {
        setCurrentUser(null);
      }
    });

    return () => unsubscribe();
  }, []);

  // Load all data from Firebase
  const loadAllData = useCallback(async () => {
    try {
      const [
        usersData,
        postsData,
        eventsData,
        lessonsData,
        settingsData,
        announcementsData
      ] = await Promise.all([
        UserService.getAllUsers(),
        PostService.getPosts(),
        EventService.getEvents(),
        LessonService.getLessons(),
        SiteSettingsService.getSiteSettings(),
        AnnouncementService.getAnnouncements()
      ]);

      setUsers(usersData);
      setPosts(postsData);
      setEvents(eventsData);
      setLessons(lessonsData);
      setSiteSettings(settingsData);
      setAnnouncements(announcementsData);

      // Load messages and comments for current user
      if (currentUser) {
        const messagesData = await MessageService.getMessages(currentUser.id);
        setMessages([...messagesData.sent, ...messagesData.received]);
        
        // Load comments for all posts
        const allComments: Comment[] = [];
        for (const post of postsData) {
          const postComments = await CommentService.getComments(post.id);
          allComments.push(...postComments);
        }
        setComments(allComments);
      }
    } catch (error) {
      console.error('Error loading data:', error);
    }
  }, [currentUser]);

  // Reload data when current user changes
  useEffect(() => {
    if (isInitialized && currentUser) {
      loadAllData();
    }
  }, [currentUser, isInitialized, loadAllData]);

  // Auth functions
  const login = useCallback(async (email: string, password: string): Promise<User | null> => {
    try {
      const user = await AuthService.signIn(email, password);
      if (user) {
        setCurrentUser(user);
        return user;
      }
      return null;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }, []);

  const signup = useCallback(async (userData: Omit<User, 'id' | 'profilePhotoUrl' | 'bannerImageUrl' | 'tags' | 'socialLinks'> & Partial<Pick<User, 'tags' | 'socialLinks'>>): Promise<User | null> => {
    try {
      const user = await AuthService.signUp(userData.email, userData.password!, userData);
      if (user) {
        setCurrentUser(user);
        await loadAllData(); // Refresh data
        return user;
      }
      return null;
    } catch (error) {
      console.error('Signup error:', error);
      throw error;
    }
  }, [loadAllData]);

  const logout = useCallback(async () => {
    try {
      await AuthService.signOut();
      setCurrentUser(null);
    } catch (error) {
      console.error('Logout error:', error);
    }
  }, []);

  // User functions
  const updateUserProfile = useCallback(async (userId: string, data: Partial<User>): Promise<User | null> => {
    try {
      const success = await UserService.updateUser(userId, data);
      if (success) {
        const updatedUser = await UserService.getUserById(userId);
        if (updatedUser && currentUser?.id === userId) {
          setCurrentUser(updatedUser);
        }
        await loadAllData(); // Refresh users list
        return updatedUser;
      }
      return null;
    } catch (error) {
      console.error('Error updating user profile:', error);
      return null;
    }
  }, [currentUser, loadAllData]);

  const getUserById = useCallback((userId: string): User | null => {
    return users.find(u => u.id === userId) || null;
  }, [users]);

  // Post functions
  const createPost = useCallback(async (postData: Omit<Post, 'id' | 'likes' | 'createdAt' | 'comments'>): Promise<Post | null> => {
    if (!currentUser || currentUser.role !== UserRole.ARTIST) {
      throw new Error("Only artists can create posts.");
    }
    
    try {
      const newPost = await PostService.createPost(postData);
      if (newPost) {
        setPosts(prev => [newPost, ...prev]);
        return newPost;
      }
      return null;
    } catch (error) {
      console.error('Error creating post:', error);
      return null;
    }
  }, [currentUser]);

  const getPosts = useCallback((filters?: { category?: PostCategory; userId?: string }): Post[] => {
    return posts
      .filter(p => (!filters?.category || p.category === filters.category))
      .filter(p => (!filters?.userId || p.userId === filters.userId))
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }, [posts]);

  // Event functions
  const createEvent = useCallback(async (eventData: Omit<Event, 'id' | 'rsvps' | 'createdAt'>): Promise<Event | null> => {
    if (!currentUser || currentUser.role !== UserRole.EDUCATOR) {
      throw new Error("Only educators can create events.");
    }
    
    try {
      const newEvent = await EventService.createEvent(eventData);
      if (newEvent) {
        setEvents(prev => [newEvent, ...prev]);
        return newEvent;
      }
      return null;
    } catch (error) {
      console.error('Error creating event:', error);
      return null;
    }
  }, [currentUser]);

  const getEvents = useCallback((filters?: { educatorId?: string }): Event[] => {
    return events
      .filter(e => (!filters?.educatorId || e.educatorId === filters.educatorId))
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }, [events]);

  const rsvpToEvent = useCallback(async (eventId: string, userId: string): Promise<boolean> => {
    try {
      const success = await EventService.rsvpToEvent(eventId, userId);
      if (success) {
        await loadAllData(); // Refresh events
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error RSVPing to event:', error);
      return false;
    }
  }, [loadAllData]);

  // Follow/Save functions
  const followArtist = useCallback(async (fanId: string, artistId: string): Promise<boolean> => {
    try {
      const user = await UserService.getUserById(fanId);
      if (!user) return false;
      
      const followedArtistIds = user.followedArtistIds || [];
      const isFollowing = followedArtistIds.includes(artistId);
      
      const updatedFollowedIds = isFollowing
        ? followedArtistIds.filter(id => id !== artistId)
        : [...followedArtistIds, artistId];
      
      const success = await UserService.updateUser(fanId, {
        followedArtistIds: updatedFollowedIds
      });
      
      if (success) {
        await loadAllData();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error following artist:', error);
      return false;
    }
  }, [loadAllData]);

  const savePost = useCallback(async (fanId: string, postId: string): Promise<boolean> => {
    try {
      const user = await UserService.getUserById(fanId);
      if (!user) return false;
      
      const savedPostIds = user.savedPostIds || [];
      const isSaved = savedPostIds.includes(postId);
      
      const updatedSavedIds = isSaved
        ? savedPostIds.filter(id => id !== postId)
        : [...savedPostIds, postId];
      
      const success = await UserService.updateUser(fanId, {
        savedPostIds: updatedSavedIds
      });
      
      if (success) {
        // Update post likes
        if (isSaved) {
          await PostService.unlikePost(postId);
        } else {
          await PostService.likePost(postId);
        }
        
        await loadAllData();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error saving post:', error);
      return false;
    }
  }, [loadAllData]);

  // Lesson functions
  const createLesson = useCallback(async (lessonData: Omit<Lesson, 'id' | 'createdAt'>): Promise<Lesson | null> => {
    if (!currentUser || currentUser.role !== UserRole.EDUCATOR) {
      throw new Error("Only educators can create lessons.");
    }

    try {
      const newLesson = await LessonService.createLesson(lessonData);
      if (newLesson) {
        setLessons(prev => [newLesson, ...prev]);

        // Update user's lessonsOffered
        const updatedLessons = [...(currentUser.lessonsOffered || []), newLesson];
        await updateUserProfile(currentUser.id, { lessonsOffered: updatedLessons });

        return newLesson;
      }
      return null;
    } catch (error) {
      console.error('Error creating lesson:', error);
      return null;
    }
  }, [currentUser, updateUserProfile]);

  const updateLesson = useCallback(async (lessonId: string, updates: Partial<Lesson>): Promise<boolean> => {
    if (!currentUser || currentUser.role !== UserRole.EDUCATOR) {
      throw new Error("Only educators can update lessons.");
    }

    try {
      const success = await LessonService.updateLesson(lessonId, updates);
      if (success) {
        await loadAllData();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error updating lesson:', error);
      return false;
    }
  }, [currentUser, loadAllData]);

  const deleteLesson = useCallback(async (lessonId: string): Promise<boolean> => {
    if (!currentUser || currentUser.role !== UserRole.EDUCATOR) {
      throw new Error("Only educators can delete lessons.");
    }

    try {
      const success = await LessonService.deleteLesson(lessonId);
      if (success) {
        await loadAllData();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error deleting lesson:', error);
      return false;
    }
  }, [currentUser, loadAllData]);

  const getLessons = useCallback((filters?: { educatorId?: string }): Lesson[] => {
    return lessons
      .filter(l => (!filters?.educatorId || l.educatorId === filters.educatorId))
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }, [lessons]);

  // Message functions
  const sendMessage = useCallback(async (messageData: Omit<Message, 'id' | 'createdAt'>): Promise<Message | null> => {
    if (!currentUser) {
      throw new Error("You must be logged in to send messages.");
    }

    try {
      const newMessage = await MessageService.sendMessage(messageData);
      if (newMessage) {
        setMessages(prev => [newMessage, ...prev]);
        return newMessage;
      }
      return null;
    } catch (error) {
      console.error('Error sending message:', error);
      return null;
    }
  }, [currentUser]);

  const getMessages = useCallback((userId: string) => {
    const sent = messages.filter(m => m.senderId === userId)
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    const received = messages.filter(m => m.receiverId === userId)
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    return { sent, received };
  }, [messages]);

  const markMessageAsRead = useCallback(async (messageId: string): Promise<boolean> => {
    if (!currentUser) return false;

    try {
      const success = await MessageService.markMessageAsRead(messageId, currentUser.id);
      if (success) {
        await loadAllData();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error marking message as read:', error);
      return false;
    }
  }, [currentUser, loadAllData]);

  const deleteMessage = useCallback(async (messageId: string): Promise<boolean> => {
    try {
      const success = await MessageService.deleteMessage(messageId);
      if (success) {
        setMessages(prev => prev.filter(m => m.id !== messageId));
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error deleting message:', error);
      return false;
    }
  }, []);

  // Comment functions
  const addComment = useCallback(async (commentData: Omit<Comment, 'id' | 'createdAt'>): Promise<Comment | null> => {
    if (!currentUser) {
      throw new Error("You must be logged in to comment.");
    }

    try {
      const newComment = await CommentService.addComment(commentData);
      if (newComment) {
        setComments(prev => [newComment, ...prev]);

        // Update the post's comments array in local state
        setPosts(prev => prev.map(post =>
          post.id === commentData.postId
            ? { ...post, comments: [...post.comments, newComment] }
            : post
        ));

        return newComment;
      }
      return null;
    } catch (error) {
      console.error('Error adding comment:', error);
      return null;
    }
  }, [currentUser]);

  const getComments = useCallback((postId: string): Comment[] => {
    return comments
      .filter(c => c.postId === postId)
      .sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
  }, [comments]);

  const deleteComment = useCallback(async (commentId: string): Promise<boolean> => {
    if (!currentUser) return false;

    const comment = comments.find(c => c.id === commentId);
    if (!comment || (comment.userId !== currentUser.id && currentUser.role !== UserRole.ADMIN)) {
      return false;
    }

    try {
      const success = await CommentService.deleteComment(commentId, comment.postId);
      if (success) {
        setComments(prev => prev.filter(c => c.id !== commentId));

        // Update the post's comments array in local state
        setPosts(prev => prev.map(post =>
          post.id === comment.postId
            ? { ...post, comments: post.comments.filter(c => c.id !== commentId) }
            : post
        ));

        return true;
      }
      return false;
    } catch (error) {
      console.error('Error deleting comment:', error);
      return false;
    }
  }, [currentUser, comments]);

  // Admin functions
  const deleteUser = useCallback(async (userId: string): Promise<boolean> => {
    if (!currentUser || currentUser.role !== UserRole.ADMIN) {
      throw new Error('Only admins can delete users.');
    }
    if (userId === currentUser.id) {
      throw new Error('Cannot delete your own admin account.');
    }

    try {
      const success = await UserService.deleteUser(userId);
      if (success) {
        await loadAllData();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error deleting user:', error);
      return false;
    }
  }, [currentUser, loadAllData]);

  const updateUserRole = useCallback(async (userId: string, newRole: UserRole): Promise<boolean> => {
    if (!currentUser || currentUser.role !== UserRole.ADMIN) {
      throw new Error('Only admins can update user roles.');
    }
    if (userId === currentUser.id && newRole !== UserRole.ADMIN) {
      throw new Error('Cannot change your own admin role.');
    }

    try {
      const success = await UserService.updateUser(userId, { role: newRole });
      if (success) {
        await loadAllData();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error updating user role:', error);
      return false;
    }
  }, [currentUser, loadAllData]);

  const toggleUserStatus = useCallback(async (userId: string): Promise<boolean> => {
    if (!currentUser || currentUser.role !== UserRole.ADMIN) {
      throw new Error('Only admins can toggle user status.');
    }
    if (userId === currentUser.id) {
      throw new Error('Cannot deactivate your own admin account.');
    }

    try {
      const user = await UserService.getUserById(userId);
      if (!user) return false;

      const success = await UserService.updateUser(userId, { isActive: !user.isActive });
      if (success) {
        await loadAllData();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error toggling user status:', error);
      return false;
    }
  }, [currentUser, loadAllData]);

  const deletePost = useCallback(async (postId: string): Promise<boolean> => {
    if (!currentUser || currentUser.role !== UserRole.ADMIN) {
      throw new Error('Only admins can delete posts.');
    }

    try {
      const success = await PostService.deletePost(postId);
      if (success) {
        setPosts(prev => prev.filter(p => p.id !== postId));
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error deleting post:', error);
      return false;
    }
  }, [currentUser]);

  const deleteEvent = useCallback(async (eventId: string): Promise<boolean> => {
    if (!currentUser || currentUser.role !== UserRole.ADMIN) {
      throw new Error('Only admins can delete events.');
    }

    try {
      const success = await EventService.deleteEvent(eventId);
      if (success) {
        setEvents(prev => prev.filter(e => e.id !== eventId));
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error deleting event:', error);
      return false;
    }
  }, [currentUser]);

  const getUserStats = useCallback(() => {
    const totalUsers = users.length;
    const activeUsers = users.filter(u => u.isActive !== false).length;
    const usersByRole = users.reduce((acc, user) => {
      acc[user.role] = (acc[user.role] || 0) + 1;
      return acc;
    }, {} as Record<UserRole, number>);

    return { totalUsers, activeUsers, usersByRole };
  }, [users]);

  // Enhanced admin functions
  const getSiteSettings = useCallback(() => {
    return siteSettings;
  }, [siteSettings]);

  const updateSiteSettings = useCallback(async (settings: Partial<SiteSettings>): Promise<boolean> => {
    if (!currentUser || currentUser.role !== UserRole.ADMIN) {
      throw new Error('Only admins can update site settings.');
    }

    try {
      const success = await SiteSettingsService.updateSiteSettings(settings);
      if (success) {
        setSiteSettings(prev => ({ ...prev, ...settings } as SiteSettings));
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error updating site settings:', error);
      return false;
    }
  }, [currentUser]);

  const getSystemHealth = useCallback((): SystemHealth => {
    const now = new Date();

    return {
      uptime: 24, // Simulated uptime in hours
      memoryUsage: Math.floor(Math.random() * 30) + 40, // Random between 40-70%
      activeUsers: users.filter(u => u.isActive !== false).length,
      errorCount: Math.floor(Math.random() * 5), // Random errors
      lastBackup: new Date(now.getTime() - 6 * 60 * 60 * 1000).toISOString() // 6 hours ago
    };
  }, [users]);

  const getContentStats = useCallback((): ContentStats => {
    const now = new Date();
    const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const postsThisWeek = posts.filter(post => new Date(post.createdAt) > lastWeek).length;
    const eventsThisWeek = events.filter(event =>
      event.createdAt && new Date(event.createdAt) > lastWeek
    ).length;

    const topCategories = Object.values(PostCategory).map(category => ({
      category,
      count: posts.filter(post => post.category === category).length
    })).sort((a, b) => b.count - a.count);

    const totalLikes = posts.reduce((sum, post) => sum + post.likes, 0);
    const engagementRate = posts.length > 0 ? (totalLikes / posts.length) : 0;

    return {
      totalPosts: posts.length,
      totalEvents: events.length,
      postsThisWeek,
      eventsThisWeek,
      topCategories,
      engagementRate
    };
  }, [posts, events]);

  // Announcement functions
  const createAnnouncement = useCallback(async (announcementData: Omit<Announcement, 'id' | 'createdAt'>): Promise<Announcement | null> => {
    if (!currentUser || currentUser.role !== UserRole.ADMIN) {
      throw new Error('Only admins can create announcements.');
    }

    try {
      const newAnnouncement = await AnnouncementService.createAnnouncement(announcementData);
      if (newAnnouncement) {
        setAnnouncements(prev => [newAnnouncement, ...prev]);
        return newAnnouncement;
      }
      return null;
    } catch (error) {
      console.error('Error creating announcement:', error);
      return null;
    }
  }, [currentUser]);

  const getAnnouncements = useCallback(() => {
    return announcements.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }, [announcements]);

  const updateAnnouncement = useCallback(async (id: string, data: Partial<Announcement>): Promise<boolean> => {
    if (!currentUser || currentUser.role !== UserRole.ADMIN) {
      throw new Error('Only admins can update announcements.');
    }

    try {
      const success = await AnnouncementService.updateAnnouncement(id, data);
      if (success) {
        setAnnouncements(prev => prev.map(announcement =>
          announcement.id === id ? { ...announcement, ...data } : announcement
        ));
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error updating announcement:', error);
      return false;
    }
  }, [currentUser]);

  const deleteAnnouncement = useCallback(async (id: string): Promise<boolean> => {
    if (!currentUser || currentUser.role !== UserRole.ADMIN) {
      throw new Error('Only admins can delete announcements.');
    }

    try {
      const success = await AnnouncementService.deleteAnnouncement(id);
      if (success) {
        setAnnouncements(prev => prev.filter(announcement => announcement.id !== id));
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error deleting announcement:', error);
      return false;
    }
  }, [currentUser]);

  // Bulk operations
  const bulkDeletePosts = useCallback(async (postIds: string[]): Promise<boolean> => {
    if (!currentUser || currentUser.role !== UserRole.ADMIN) {
      throw new Error('Only admins can bulk delete posts.');
    }

    try {
      const promises = postIds.map(id => PostService.deletePost(id));
      await Promise.all(promises);
      setPosts(prev => prev.filter(post => !postIds.includes(post.id)));
      return true;
    } catch (error) {
      console.error('Error bulk deleting posts:', error);
      return false;
    }
  }, [currentUser]);

  const bulkDeleteEvents = useCallback(async (eventIds: string[]): Promise<boolean> => {
    if (!currentUser || currentUser.role !== UserRole.ADMIN) {
      throw new Error('Only admins can bulk delete events.');
    }

    try {
      const promises = eventIds.map(id => EventService.deleteEvent(id));
      await Promise.all(promises);
      setEvents(prev => prev.filter(event => !eventIds.includes(event.id)));
      return true;
    } catch (error) {
      console.error('Error bulk deleting events:', error);
      return false;
    }
  }, [currentUser]);

  const bulkUpdateUsers = useCallback(async (userIds: string[], updates: Partial<User>): Promise<boolean> => {
    if (!currentUser || currentUser.role !== UserRole.ADMIN) {
      throw new Error('Only admins can bulk update users.');
    }

    try {
      const promises = userIds.map(id => UserService.updateUser(id, updates));
      await Promise.all(promises);
      await loadAllData();
      return true;
    } catch (error) {
      console.error('Error bulk updating users:', error);
      return false;
    }
  }, [currentUser, loadAllData]);

  // Export functions
  const exportUserData = useCallback(() => {
    const exportData = {
      users: users.map(user => ({
        ...user,
        password: undefined // Don't export passwords
      })),
      exportDate: new Date().toISOString(),
      totalUsers: users.length
    };
    return JSON.stringify(exportData, null, 2);
  }, [users]);

  const exportContentData = useCallback(() => {
    const exportData = {
      posts,
      events,
      exportDate: new Date().toISOString(),
      totalPosts: posts.length,
      totalEvents: events.length
    };
    return JSON.stringify(exportData, null, 2);
  }, [posts, events]);

  // Reset function (for development)
  const resetToInitialData = useCallback(async () => {
    try {
      // Clear local state
      setCurrentUser(null);
      setUsers([]);
      setPosts([]);
      setEvents([]);
      setLessons([]);
      setMessages([]);
      setComments([]);
      setSiteSettings(null);
      setAnnouncements([]);

      // Sign out
      await AuthService.signOut();

      // Clear migration flag to allow re-initialization
      localStorage.removeItem('firebase_migration_completed');
      localStorage.removeItem('firebase_migration_date');

      // Reinitialize
      await DataMigrationService.initializeDefaultData();
      await loadAllData();

      console.log('Data reset completed');
    } catch (error) {
      console.error('Error resetting data:', error);
    }
  }, [loadAllData]);

  // Loading spinner component
  const SpinnerIcon: React.FC<{ className?: string }> = ({ className }) => (
    <svg className={`animate-spin h-5 w-5 ${className}`} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <SpinnerIcon className="w-12 h-12 text-primary" />
        <span className="ml-2 text-lg">Initializing Firebase...</span>
      </div>
    );
  }

  return (
    <FirebaseAppContext.Provider value={{
      currentUser, users, posts, events, lessons, messages, comments,
      login, signup, logout, updateUserProfile, getUserById,
      createPost, getPosts, createEvent, getEvents,
      rsvpToEvent, followArtist, savePost,
      // Lesson functions
      createLesson, updateLesson, deleteLesson, getLessons,
      // Message functions
      sendMessage, getMessages, markMessageAsRead, deleteMessage,
      // Comment functions
      addComment, getComments, deleteComment,
      // Admin functions
      deleteUser, updateUserRole, toggleUserStatus, deletePost, deleteEvent, getUserStats, resetToInitialData,
      // Enhanced admin functions
      getSiteSettings, updateSiteSettings, getSystemHealth, getContentStats,
      createAnnouncement, getAnnouncements, updateAnnouncement, deleteAnnouncement,
      bulkDeletePosts, bulkDeleteEvents, bulkUpdateUsers, exportUserData, exportContentData
    }}>
      {isInitialized ? children : (
        <div className="flex items-center justify-center h-screen">
          <SpinnerIcon className="w-12 h-12 text-primary" />
          <span className="ml-2 text-lg">Loading application...</span>
        </div>
      )}
    </FirebaseAppContext.Provider>
  );
};

export const useFirebaseApp = (): AppContextType => {
  const context = React.useContext(FirebaseAppContext);
  if (context === undefined) {
    throw new Error('useFirebaseApp must be used within a FirebaseAppProvider');
  }
  return context;
};
