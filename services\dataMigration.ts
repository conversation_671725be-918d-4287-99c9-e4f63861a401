// Data migration utilities for moving from localStorage to Firebase
import { 
  UserService, 
  PostService, 
  EventService, 
  LessonService, 
  MessageService, 
  CommentService, 
  SiteSettingsService, 
  AnnouncementService 
} from './firebaseService';
import { AdminSeederService } from './adminSeeder';
import { User, Post, Event, Lesson, Message, Comment, SiteSettings, Announcement } from '../types';

export class DataMigrationService {
  
  // Check if migration is needed
  static isMigrationNeeded(): boolean {
    const hasLocalData = 
      localStorage.getItem('mac_users') ||
      localStorage.getItem('mac_posts') ||
      localStorage.getItem('mac_events') ||
      localStorage.getItem('mac_lessons') ||
      localStorage.getItem('mac_messages') ||
      localStorage.getItem('mac_comments') ||
      localStorage.getItem('mac_siteSettings') ||
      localStorage.getItem('mac_announcements');
    
    const migrationCompleted = localStorage.getItem('firebase_migration_completed');
    
    return !!hasLocalData && !migrationCompleted;
  }
  
  // Get data from localStorage
  static getLocalStorageData() {
    const users = this.parseLocalStorageItem('mac_users', []);
    const posts = this.parseLocalStorageItem('mac_posts', []);
    const events = this.parseLocalStorageItem('mac_events', []);
    const lessons = this.parseLocalStorageItem('mac_lessons', []);
    const messages = this.parseLocalStorageItem('mac_messages', []);
    const comments = this.parseLocalStorageItem('mac_comments', []);
    const siteSettings = this.parseLocalStorageItem('mac_siteSettings', null);
    const announcements = this.parseLocalStorageItem('mac_announcements', []);
    
    return {
      users,
      posts,
      events,
      lessons,
      messages,
      comments,
      siteSettings,
      announcements
    };
  }
  
  private static parseLocalStorageItem(key: string, defaultValue: any) {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
      console.error(`Error parsing localStorage item ${key}:`, error);
      return defaultValue;
    }
  }
  
  // Migrate all data to Firebase
  static async migrateAllData(): Promise<{ success: boolean; errors: string[] }> {
    const errors: string[] = [];
    
    try {
      console.log('Starting data migration to Firebase...');
      
      const localData = this.getLocalStorageData();
      
      // Migrate site settings first
      if (localData.siteSettings) {
        try {
          await SiteSettingsService.updateSiteSettings(localData.siteSettings);
          console.log('✓ Site settings migrated');
        } catch (error) {
          errors.push(`Site settings migration failed: ${error}`);
        }
      }
      
      // Migrate users (but skip creating auth accounts for existing users)
      const userIdMap = new Map<string, string>(); // old ID -> new ID
      for (const user of localData.users) {
        try {
          // For migration, we'll create user documents directly without auth
          // In a real scenario, users would need to re-register
          const userData = { ...user };
          delete userData.id; // Remove old ID
          delete userData.password; // Remove password
          
          // Note: In production, you'd handle auth differently
          // For now, we'll just create the user documents
          console.log(`Migrating user: ${user.displayName}`);
        } catch (error) {
          errors.push(`User migration failed for ${user.displayName}: ${error}`);
        }
      }
      
      // Migrate posts
      for (const post of localData.posts) {
        try {
          const postData = { ...post };
          delete postData.id; // Remove old ID
          await PostService.createPost(postData);
          console.log(`✓ Post migrated: ${post.title}`);
        } catch (error) {
          errors.push(`Post migration failed for ${post.title}: ${error}`);
        }
      }
      
      // Migrate events
      for (const event of localData.events) {
        try {
          const eventData = { ...event };
          delete eventData.id; // Remove old ID
          await EventService.createEvent(eventData);
          console.log(`✓ Event migrated: ${event.title}`);
        } catch (error) {
          errors.push(`Event migration failed for ${event.title}: ${error}`);
        }
      }
      
      // Migrate lessons
      for (const lesson of localData.lessons) {
        try {
          const lessonData = { ...lesson };
          delete lessonData.id; // Remove old ID
          await LessonService.createLesson(lessonData);
          console.log(`✓ Lesson migrated: ${lesson.title}`);
        } catch (error) {
          errors.push(`Lesson migration failed for ${lesson.title}: ${error}`);
        }
      }
      
      // Migrate messages
      for (const message of localData.messages) {
        try {
          const messageData = { ...message };
          delete messageData.id; // Remove old ID
          await MessageService.sendMessage(messageData);
          console.log(`✓ Message migrated`);
        } catch (error) {
          errors.push(`Message migration failed: ${error}`);
        }
      }
      
      // Migrate comments
      for (const comment of localData.comments) {
        try {
          const commentData = { ...comment };
          delete commentData.id; // Remove old ID
          await CommentService.addComment(commentData);
          console.log(`✓ Comment migrated`);
        } catch (error) {
          errors.push(`Comment migration failed: ${error}`);
        }
      }
      
      // Migrate announcements
      for (const announcement of localData.announcements) {
        try {
          const announcementData = { ...announcement };
          delete announcementData.id; // Remove old ID
          await AnnouncementService.createAnnouncement(announcementData);
          console.log(`✓ Announcement migrated: ${announcement.title}`);
        } catch (error) {
          errors.push(`Announcement migration failed for ${announcement.title}: ${error}`);
        }
      }
      
      // Mark migration as completed
      localStorage.setItem('firebase_migration_completed', 'true');
      localStorage.setItem('firebase_migration_date', new Date().toISOString());
      
      console.log('Data migration completed!');
      
      return {
        success: errors.length === 0,
        errors
      };
      
    } catch (error) {
      console.error('Migration failed:', error);
      return {
        success: false,
        errors: [`Migration failed: ${error}`]
      };
    }
  }
  
  // Clear localStorage after successful migration
  static clearLocalStorageData(): void {
    const keysToRemove = [
      'mac_users',
      'mac_posts',
      'mac_events',
      'mac_lessons',
      'mac_messages',
      'mac_comments',
      'mac_siteSettings',
      'mac_announcements',
      'currentUser'
    ];
    
    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
    });
    
    console.log('Local storage data cleared');
  }
  
  // Initialize Firebase with default data if no data exists
  static async initializeDefaultData(): Promise<void> {
    try {
      console.log('Initializing Firebase with default data...');

      // Try to ensure admin user exists, but don't block initialization if it fails
      try {
        await AdminSeederService.ensureAdminExists();
      } catch (error) {
        console.warn('Admin seeding failed, but continuing initialization:', error);
      }

      // Check if we already have other data in Firebase
      const existingUsers = await UserService.getAllUsers();
      const hasNonAdminData = existingUsers.length > 1; // More than just the admin

      if (hasNonAdminData) {
        console.log('Firebase already has additional data, skipping other initialization');
        return;
      }

      // Initialize with default site settings
      const defaultSiteSettings = {
        siteName: 'The MusicArt Club',
        siteDescription: 'A vibrant creative community where music and art enthusiasts connect, share, and grow together.',
        contactEmail: '<EMAIL>',
        maintenanceMode: false,
        allowRegistration: true,
        featuredPostsLimit: 5,
        theme: {
          primaryColor: '#f97316',
          secondaryColor: '#8b5cf6',
          accentColor: '#06b6d4'
        }
      };

      await SiteSettingsService.updateSiteSettings(defaultSiteSettings);

      // Create default announcement
      await AnnouncementService.createAnnouncement({
        title: 'Welcome to The MusicArt Club!',
        message: 'We\'re excited to have you join our creative community. Explore, create, and connect with fellow artists and music lovers.',
        type: 'success',
        isActive: true,
        targetRoles: []
      });

      console.log('✓ Default data initialized');

    } catch (error) {
      console.error('Error initializing default data:', error);
    }
  }
}
