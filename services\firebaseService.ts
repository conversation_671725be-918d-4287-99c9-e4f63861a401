// Firebase service layer for database operations
import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  Timestamp,
  writeBatch,
  increment,
  arrayUnion,
  arrayRemove,
  onSnapshot,
  QuerySnapshot,
  DocumentData,
  serverTimestamp
} from 'firebase/firestore';
import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  updateProfile,
  User as FirebaseUser,
  onAuthStateChanged
} from 'firebase/auth';
import {
  ref,
  uploadBytes,
  getDownloadURL,
  deleteObject
} from 'firebase/storage';
import { db, auth, storage } from '../firebase';
import { User, Post, Event, Lesson, Message, Comment, UserRole, PostCategory, SiteSettings, Announcement } from '../types';

// Collection names
export const COLLECTIONS = {
  USERS: 'users',
  POSTS: 'posts',
  EVENTS: 'events',
  LESSONS: 'lessons',
  MESSAGES: 'messages',
  COMMENTS: 'comments',
  SITE_SETTINGS: 'siteSettings',
  ANNOUNCEMENTS: 'announcements'
} as const;

// Helper function to convert Firestore timestamp to ISO string
const timestampToISO = (timestamp: any): string => {
  if (timestamp?.toDate) {
    return timestamp.toDate().toISOString();
  }
  return timestamp || new Date().toISOString();
};

// Helper function to convert data from Firestore
const convertFirestoreData = (doc: any): any => {
  const data = doc.data();
  const converted = { id: doc.id, ...data };
  
  // Convert timestamps
  if (converted.createdAt) converted.createdAt = timestampToISO(converted.createdAt);
  if (converted.lastLoginAt) converted.lastLoginAt = timestampToISO(converted.lastLoginAt);
  if (converted.dateTime) converted.dateTime = timestampToISO(converted.dateTime);
  if (converted.expiresAt) converted.expiresAt = timestampToISO(converted.expiresAt);
  
  return converted;
};

// Auth Service
export class AuthService {
  static async signUp(email: string, password: string, userData: Partial<User>): Promise<User | null> {
    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const firebaseUser = userCredential.user;
      
      // Update Firebase Auth profile
      await updateProfile(firebaseUser, {
        displayName: userData.displayName
      });
      
      // Create user document in Firestore
      const newUser: Omit<User, 'id'> = {
        email: firebaseUser.email!,
        role: userData.role || UserRole.FAN,
        displayName: userData.displayName || '',
        profilePhotoUrl: userData.profilePhotoUrl || `https://picsum.photos/seed/${userData.displayName}/200/200`,
        bannerImageUrl: userData.bannerImageUrl || `https://picsum.photos/seed/${userData.displayName}-banner/1200/400`,
        location: userData.location,
        bio: userData.bio,
        tags: userData.tags || [],
        socialLinks: userData.socialLinks || {},
        artistStatement: userData.artistStatement,
        genre: userData.genre,
        lessonsOffered: userData.role === UserRole.EDUCATOR ? [] : undefined,
        followedArtistIds: userData.role === UserRole.FAN ? [] : undefined,
        savedPostIds: userData.role === UserRole.FAN ? [] : undefined,
        rsvpedEventIds: userData.role === UserRole.FAN ? [] : undefined,
        isActive: true,
        createdAt: serverTimestamp(),
        lastLoginAt: serverTimestamp(),
        unreadMessageCount: 0
      };
      
      const userDoc = await addDoc(collection(db, COLLECTIONS.USERS), newUser);
      return { id: userDoc.id, ...newUser, createdAt: new Date().toISOString(), lastLoginAt: new Date().toISOString() } as User;
    } catch (error) {
      console.error('Error signing up:', error);
      throw error;
    }
  }
  
  static async signIn(email: string, password: string): Promise<User | null> {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const firebaseUser = userCredential.user;
      
      // Get user document from Firestore
      const userQuery = query(
        collection(db, COLLECTIONS.USERS),
        where('email', '==', firebaseUser.email)
      );
      const userSnapshot = await getDocs(userQuery);
      
      if (!userSnapshot.empty) {
        const userDoc = userSnapshot.docs[0];
        const userData = convertFirestoreData(userDoc);
        
        // Update last login time
        await updateDoc(userDoc.ref, {
          lastLoginAt: serverTimestamp()
        });
        
        return { ...userData, lastLoginAt: new Date().toISOString() } as User;
      }
      
      return null;
    } catch (error) {
      console.error('Error signing in:', error);
      throw error;
    }
  }
  
  static async signOut(): Promise<void> {
    try {
      await signOut(auth);
    } catch (error) {
      console.error('Error signing out:', error);
      throw error;
    }
  }
  
  static onAuthStateChanged(callback: (user: FirebaseUser | null) => void) {
    return onAuthStateChanged(auth, callback);
  }
}

// User Service
export class UserService {
  static async getUserById(userId: string): Promise<User | null> {
    try {
      const userDoc = await getDoc(doc(db, COLLECTIONS.USERS, userId));
      if (userDoc.exists()) {
        return convertFirestoreData(userDoc) as User;
      }
      return null;
    } catch (error) {
      console.error('Error getting user:', error);
      return null;
    }
  }
  
  static async updateUser(userId: string, updates: Partial<User>): Promise<boolean> {
    try {
      const userRef = doc(db, COLLECTIONS.USERS, userId);
      const updateData = { ...updates };
      
      // Convert date strings to timestamps if needed
      if (updateData.createdAt) delete updateData.createdAt; // Don't update creation time
      if (updateData.lastLoginAt) updateData.lastLoginAt = serverTimestamp();
      
      await updateDoc(userRef, updateData);
      return true;
    } catch (error) {
      console.error('Error updating user:', error);
      return false;
    }
  }
  
  static async getAllUsers(): Promise<User[]> {
    try {
      const usersSnapshot = await getDocs(collection(db, COLLECTIONS.USERS));
      return usersSnapshot.docs.map(doc => convertFirestoreData(doc) as User);
    } catch (error) {
      console.error('Error getting users:', error);
      return [];
    }
  }
  
  static async deleteUser(userId: string): Promise<boolean> {
    try {
      await deleteDoc(doc(db, COLLECTIONS.USERS, userId));
      return true;
    } catch (error) {
      console.error('Error deleting user:', error);
      return false;
    }
  }
}

// Post Service
export class PostService {
  static async createPost(postData: Omit<Post, 'id' | 'likes' | 'createdAt' | 'comments'>): Promise<Post | null> {
    try {
      const newPost = {
        ...postData,
        likes: 0,
        comments: [],
        createdAt: serverTimestamp()
      };
      
      const postDoc = await addDoc(collection(db, COLLECTIONS.POSTS), newPost);
      return { 
        id: postDoc.id, 
        ...newPost, 
        createdAt: new Date().toISOString() 
      } as Post;
    } catch (error) {
      console.error('Error creating post:', error);
      return null;
    }
  }
  
  static async getPosts(filters?: { category?: PostCategory; userId?: string }): Promise<Post[]> {
    try {
      let q = query(collection(db, COLLECTIONS.POSTS), orderBy('createdAt', 'desc'));
      
      if (filters?.category) {
        q = query(q, where('category', '==', filters.category));
      }
      
      if (filters?.userId) {
        q = query(q, where('userId', '==', filters.userId));
      }
      
      const postsSnapshot = await getDocs(q);
      return postsSnapshot.docs.map(doc => convertFirestoreData(doc) as Post);
    } catch (error) {
      console.error('Error getting posts:', error);
      return [];
    }
  }
  
  static async updatePost(postId: string, updates: Partial<Post>): Promise<boolean> {
    try {
      const postRef = doc(db, COLLECTIONS.POSTS, postId);
      await updateDoc(postRef, updates);
      return true;
    } catch (error) {
      console.error('Error updating post:', error);
      return false;
    }
  }
  
  static async deletePost(postId: string): Promise<boolean> {
    try {
      await deleteDoc(doc(db, COLLECTIONS.POSTS, postId));
      return true;
    } catch (error) {
      console.error('Error deleting post:', error);
      return false;
    }
  }
  
  static async likePost(postId: string): Promise<boolean> {
    try {
      const postRef = doc(db, COLLECTIONS.POSTS, postId);
      await updateDoc(postRef, {
        likes: increment(1)
      });
      return true;
    } catch (error) {
      console.error('Error liking post:', error);
      return false;
    }
  }
  
  static async unlikePost(postId: string): Promise<boolean> {
    try {
      const postRef = doc(db, COLLECTIONS.POSTS, postId);
      await updateDoc(postRef, {
        likes: increment(-1)
      });
      return true;
    } catch (error) {
      console.error('Error unliking post:', error);
      return false;
    }
  }
}

// Event Service
export class EventService {
  static async createEvent(eventData: Omit<Event, 'id' | 'rsvps' | 'createdAt'>): Promise<Event | null> {
    try {
      const newEvent = {
        ...eventData,
        rsvps: [],
        createdAt: serverTimestamp()
      };

      const eventDoc = await addDoc(collection(db, COLLECTIONS.EVENTS), newEvent);
      return {
        id: eventDoc.id,
        ...newEvent,
        createdAt: new Date().toISOString()
      } as Event;
    } catch (error) {
      console.error('Error creating event:', error);
      return null;
    }
  }

  static async getEvents(filters?: { educatorId?: string }): Promise<Event[]> {
    try {
      let q = query(collection(db, COLLECTIONS.EVENTS), orderBy('createdAt', 'desc'));

      if (filters?.educatorId) {
        q = query(q, where('educatorId', '==', filters.educatorId));
      }

      const eventsSnapshot = await getDocs(q);
      return eventsSnapshot.docs.map(doc => convertFirestoreData(doc) as Event);
    } catch (error) {
      console.error('Error getting events:', error);
      return [];
    }
  }

  static async rsvpToEvent(eventId: string, userId: string): Promise<boolean> {
    try {
      const eventRef = doc(db, COLLECTIONS.EVENTS, eventId);
      const eventDoc = await getDoc(eventRef);

      if (eventDoc.exists()) {
        const eventData = eventDoc.data() as Event;
        const isRsvped = eventData.rsvps.includes(userId);

        await updateDoc(eventRef, {
          rsvps: isRsvped ? arrayRemove(userId) : arrayUnion(userId)
        });

        return true;
      }
      return false;
    } catch (error) {
      console.error('Error RSVPing to event:', error);
      return false;
    }
  }

  static async deleteEvent(eventId: string): Promise<boolean> {
    try {
      await deleteDoc(doc(db, COLLECTIONS.EVENTS, eventId));
      return true;
    } catch (error) {
      console.error('Error deleting event:', error);
      return false;
    }
  }
}

// Lesson Service
export class LessonService {
  static async createLesson(lessonData: Omit<Lesson, 'id' | 'createdAt'>): Promise<Lesson | null> {
    try {
      const newLesson = {
        ...lessonData,
        createdAt: serverTimestamp()
      };

      const lessonDoc = await addDoc(collection(db, COLLECTIONS.LESSONS), newLesson);
      return {
        id: lessonDoc.id,
        ...newLesson,
        createdAt: new Date().toISOString()
      } as Lesson;
    } catch (error) {
      console.error('Error creating lesson:', error);
      return null;
    }
  }

  static async getLessons(filters?: { educatorId?: string }): Promise<Lesson[]> {
    try {
      let q = query(collection(db, COLLECTIONS.LESSONS), orderBy('createdAt', 'desc'));

      if (filters?.educatorId) {
        q = query(q, where('educatorId', '==', filters.educatorId));
      }

      const lessonsSnapshot = await getDocs(q);
      return lessonsSnapshot.docs.map(doc => convertFirestoreData(doc) as Lesson);
    } catch (error) {
      console.error('Error getting lessons:', error);
      return [];
    }
  }

  static async updateLesson(lessonId: string, updates: Partial<Lesson>): Promise<boolean> {
    try {
      const lessonRef = doc(db, COLLECTIONS.LESSONS, lessonId);
      await updateDoc(lessonRef, updates);
      return true;
    } catch (error) {
      console.error('Error updating lesson:', error);
      return false;
    }
  }

  static async deleteLesson(lessonId: string): Promise<boolean> {
    try {
      await deleteDoc(doc(db, COLLECTIONS.LESSONS, lessonId));
      return true;
    } catch (error) {
      console.error('Error deleting lesson:', error);
      return false;
    }
  }
}

// Message Service
export class MessageService {
  static async sendMessage(messageData: Omit<Message, 'id' | 'createdAt'>): Promise<Message | null> {
    try {
      const newMessage = {
        ...messageData,
        isRead: false,
        createdAt: serverTimestamp()
      };

      const messageDoc = await addDoc(collection(db, COLLECTIONS.MESSAGES), newMessage);

      // Update receiver's unread message count
      const receiverRef = doc(db, COLLECTIONS.USERS, messageData.receiverId);
      await updateDoc(receiverRef, {
        unreadMessageCount: increment(1)
      });

      return {
        id: messageDoc.id,
        ...newMessage,
        createdAt: new Date().toISOString()
      } as Message;
    } catch (error) {
      console.error('Error sending message:', error);
      return null;
    }
  }

  static async getMessages(userId: string): Promise<{ sent: Message[]; received: Message[] }> {
    try {
      const sentQuery = query(
        collection(db, COLLECTIONS.MESSAGES),
        where('senderId', '==', userId),
        orderBy('createdAt', 'desc')
      );

      const receivedQuery = query(
        collection(db, COLLECTIONS.MESSAGES),
        where('receiverId', '==', userId),
        orderBy('createdAt', 'desc')
      );

      const [sentSnapshot, receivedSnapshot] = await Promise.all([
        getDocs(sentQuery),
        getDocs(receivedQuery)
      ]);

      const sent = sentSnapshot.docs.map(doc => convertFirestoreData(doc) as Message);
      const received = receivedSnapshot.docs.map(doc => convertFirestoreData(doc) as Message);

      return { sent, received };
    } catch (error) {
      console.error('Error getting messages:', error);
      return { sent: [], received: [] };
    }
  }

  static async markMessageAsRead(messageId: string, userId: string): Promise<boolean> {
    try {
      const messageRef = doc(db, COLLECTIONS.MESSAGES, messageId);
      await updateDoc(messageRef, {
        isRead: true
      });

      // Update user's unread message count
      const userRef = doc(db, COLLECTIONS.USERS, userId);
      await updateDoc(userRef, {
        unreadMessageCount: increment(-1)
      });

      return true;
    } catch (error) {
      console.error('Error marking message as read:', error);
      return false;
    }
  }

  static async deleteMessage(messageId: string): Promise<boolean> {
    try {
      await deleteDoc(doc(db, COLLECTIONS.MESSAGES, messageId));
      return true;
    } catch (error) {
      console.error('Error deleting message:', error);
      return false;
    }
  }
}

// Comment Service
export class CommentService {
  static async addComment(commentData: Omit<Comment, 'id' | 'createdAt'>): Promise<Comment | null> {
    try {
      const newComment = {
        ...commentData,
        createdAt: serverTimestamp()
      };

      const commentDoc = await addDoc(collection(db, COLLECTIONS.COMMENTS), newComment);

      // Update the post's comments array
      const postRef = doc(db, COLLECTIONS.POSTS, commentData.postId);
      const commentWithId = { id: commentDoc.id, ...newComment, createdAt: new Date().toISOString() };
      await updateDoc(postRef, {
        comments: arrayUnion(commentWithId)
      });

      return commentWithId as Comment;
    } catch (error) {
      console.error('Error adding comment:', error);
      return null;
    }
  }

  static async getComments(postId: string): Promise<Comment[]> {
    try {
      const q = query(
        collection(db, COLLECTIONS.COMMENTS),
        where('postId', '==', postId),
        orderBy('createdAt', 'asc')
      );

      const commentsSnapshot = await getDocs(q);
      return commentsSnapshot.docs.map(doc => convertFirestoreData(doc) as Comment);
    } catch (error) {
      console.error('Error getting comments:', error);
      return [];
    }
  }

  static async deleteComment(commentId: string, postId: string): Promise<boolean> {
    try {
      // Get the comment first
      const commentDoc = await getDoc(doc(db, COLLECTIONS.COMMENTS, commentId));
      if (!commentDoc.exists()) return false;

      const commentData = convertFirestoreData(commentDoc);

      // Delete from comments collection
      await deleteDoc(doc(db, COLLECTIONS.COMMENTS, commentId));

      // Remove from post's comments array
      const postRef = doc(db, COLLECTIONS.POSTS, postId);
      await updateDoc(postRef, {
        comments: arrayRemove(commentData)
      });

      return true;
    } catch (error) {
      console.error('Error deleting comment:', error);
      return false;
    }
  }
}

// Site Settings Service
export class SiteSettingsService {
  static async getSiteSettings(): Promise<SiteSettings | null> {
    try {
      const settingsSnapshot = await getDocs(collection(db, COLLECTIONS.SITE_SETTINGS));
      if (!settingsSnapshot.empty) {
        const settingsDoc = settingsSnapshot.docs[0];
        return convertFirestoreData(settingsDoc) as SiteSettings;
      }
      return null;
    } catch (error) {
      console.error('Error getting site settings:', error);
      return null;
    }
  }

  static async updateSiteSettings(settings: Partial<SiteSettings>): Promise<boolean> {
    try {
      const settingsSnapshot = await getDocs(collection(db, COLLECTIONS.SITE_SETTINGS));

      if (!settingsSnapshot.empty) {
        const settingsDoc = settingsSnapshot.docs[0];
        await updateDoc(settingsDoc.ref, settings);
      } else {
        // Create new settings document
        await addDoc(collection(db, COLLECTIONS.SITE_SETTINGS), settings);
      }

      return true;
    } catch (error) {
      console.error('Error updating site settings:', error);
      return false;
    }
  }
}

// Announcement Service
export class AnnouncementService {
  static async createAnnouncement(announcementData: Omit<Announcement, 'id' | 'createdAt'>): Promise<Announcement | null> {
    try {
      const newAnnouncement = {
        ...announcementData,
        createdAt: serverTimestamp()
      };

      const announcementDoc = await addDoc(collection(db, COLLECTIONS.ANNOUNCEMENTS), newAnnouncement);
      return {
        id: announcementDoc.id,
        ...newAnnouncement,
        createdAt: new Date().toISOString()
      } as Announcement;
    } catch (error) {
      console.error('Error creating announcement:', error);
      return null;
    }
  }

  static async getAnnouncements(): Promise<Announcement[]> {
    try {
      const q = query(collection(db, COLLECTIONS.ANNOUNCEMENTS), orderBy('createdAt', 'desc'));
      const announcementsSnapshot = await getDocs(q);
      return announcementsSnapshot.docs.map(doc => convertFirestoreData(doc) as Announcement);
    } catch (error) {
      console.error('Error getting announcements:', error);
      return [];
    }
  }

  static async updateAnnouncement(id: string, data: Partial<Announcement>): Promise<boolean> {
    try {
      const announcementRef = doc(db, COLLECTIONS.ANNOUNCEMENTS, id);
      await updateDoc(announcementRef, data);
      return true;
    } catch (error) {
      console.error('Error updating announcement:', error);
      return false;
    }
  }

  static async deleteAnnouncement(id: string): Promise<boolean> {
    try {
      await deleteDoc(doc(db, COLLECTIONS.ANNOUNCEMENTS, id));
      return true;
    } catch (error) {
      console.error('Error deleting announcement:', error);
      return false;
    }
  }
}

// Storage Service
export class StorageService {
  static async uploadFile(file: File, path: string): Promise<string | null> {
    try {
      const storageRef = ref(storage, path);
      const snapshot = await uploadBytes(storageRef, file);
      const downloadURL = await getDownloadURL(snapshot.ref);
      return downloadURL;
    } catch (error) {
      console.error('Error uploading file:', error);
      return null;
    }
  }

  static async deleteFile(path: string): Promise<boolean> {
    try {
      const storageRef = ref(storage, path);
      await deleteObject(storageRef);
      return true;
    } catch (error) {
      console.error('Error deleting file:', error);
      return false;
    }
  }

  static async uploadProfilePhoto(userId: string, file: File): Promise<string | null> {
    const path = `profile-photos/${userId}/${Date.now()}_${file.name}`;
    return this.uploadFile(file, path);
  }

  static async uploadBannerImage(userId: string, file: File): Promise<string | null> {
    const path = `banner-images/${userId}/${Date.now()}_${file.name}`;
    return this.uploadFile(file, path);
  }

  static async uploadPostMedia(userId: string, file: File): Promise<string | null> {
    const path = `post-media/${userId}/${Date.now()}_${file.name}`;
    return this.uploadFile(file, path);
  }
}
