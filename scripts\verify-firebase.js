#!/usr/bin/env node

// Firebase Setup Verification Script
// This script verifies that Firebase is properly configured

import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore, doc, setDoc, getDoc, deleteDoc } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyBhVCh-UFBHIYdnLQ0C37GHFSCGpmQOHc8",
  authDomain: "musicart-7641d.firebaseapp.com",
  projectId: "musicart-7641d",
  storageBucket: "musicart-7641d.firebasestorage.app",
  messagingSenderId: "732159852081",
  appId: "1:732159852081:web:4117b804da97bb8bc55f10",
  measurementId: "G-38F2VTX3SN"
};

async function verifyFirebaseSetup() {
  console.log('🔥 Verifying Firebase Setup...\n');
  
  try {
    // Initialize Firebase
    console.log('1. Initializing Firebase...');
    const app = initializeApp(firebaseConfig);
    console.log('   ✅ Firebase app initialized');
    
    // Initialize services
    console.log('2. Initializing Firebase services...');
    const auth = getAuth(app);
    const db = getFirestore(app);
    const storage = getStorage(app);
    console.log('   ✅ Auth, Firestore, and Storage initialized');
    
    // Test Firestore connection (without authentication)
    console.log('3. Testing Firestore connection...');
    try {
      // Just verify we can connect to Firestore (security rules will block writes)
      const testDocRef = doc(db, 'test', 'connection-test');
      await getDoc(testDocRef);
      console.log('   ✅ Firestore connection successful');
      console.log('   🔒 Security rules are active (write blocked without auth)');
    } catch (error) {
      if (error.code === 'permission-denied') {
        console.log('   ✅ Firestore connection successful');
        console.log('   🔒 Security rules are working correctly');
      } else {
        throw error;
      }
    }
    
    // Test Storage connection
    console.log('4. Testing Storage connection...');
    try {
      // Just check if storage is accessible
      const storageRef = storage._delegate || storage;
      console.log('   ✅ Storage service accessible');
      console.log('   📦 Storage bucket:', storageRef.app.options.storageBucket);
    } catch (error) {
      console.log('   ⚠️  Storage test skipped (requires file upload)');
    }
    
    // Check environment
    console.log('5. Environment check...');
    console.log('   🌍 Project ID:', firebaseConfig.projectId);
    console.log('   🔗 Auth Domain:', firebaseConfig.authDomain);
    console.log('   💾 Storage Bucket:', firebaseConfig.storageBucket);
    console.log('   📊 Analytics ID:', firebaseConfig.measurementId);
    
    console.log('\n🎉 Firebase setup verification completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Enable authentication providers in Firebase Console');
    console.log('2. Set up environment variables in Netlify');
    console.log('3. Deploy your app to Netlify');
    console.log('4. Test user registration and login');
    
    return true;
    
  } catch (error) {
    console.error('\n❌ Firebase setup verification failed:');
    console.error('Error:', error.message);
    
    if (error.code) {
      console.error('Error Code:', error.code);
    }
    
    console.log('\nTroubleshooting:');
    console.log('1. Check your internet connection');
    console.log('2. Verify Firebase project exists and is active');
    console.log('3. Check Firestore database is created');
    console.log('4. Verify security rules are deployed');
    console.log('5. Check Firebase Console for any issues');
    
    return false;
  }
}

// Run verification if this script is executed directly
verifyFirebaseSetup()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });

export { verifyFirebaseSetup };
