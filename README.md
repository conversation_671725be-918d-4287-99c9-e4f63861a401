# The MusicArt Club 🎨🎵

A vibrant creative community where music and art enthusiasts connect, share, and grow together.

## 🔥 Firebase-Powered Features

This application is now fully integrated with Firebase, providing:
- **Real-time Database** with Firestore
- **User Authentication** with role-based access
- **File Storage** for media uploads
- **Hosting** for production deployment
- **Analytics** for user insights

## 🚀 Quick Start

**Prerequisites:** Node.js (v16+)

### 1. Install Dependencies
```bash
npm install
```

### 2. Firebase Setup
```bash
# Automated setup (recommended)
chmod +x scripts/firebase-setup.sh
./scripts/firebase-setup.sh

# Or manual setup - see FIREBASE_SETUP.md
```

### 3. Environment Configuration
Copy `.env.example` to `.env.local` and configure:
```bash
cp .env.example .env.local
```

### 4. Run Locally
```bash
# With Firebase emulators (recommended for development)
npm run firebase:emulators
npm run dev

# Or without emulators
npm run dev
```

## 📖 Documentation

- **[Firebase Setup Guide](FIREBASE_SETUP.md)** - Complete Firebase configuration
- **[User Roles & Permissions](FIREBASE_SETUP.md#user-roles--permissions)** - Role-based access control
- **[Database Structure](FIREBASE_SETUP.md#database-structure)** - Collections and indexes

## 🎭 User Types & Features

### 🎨 Artists
- Create and share visual art, music, and performances
- Build a portfolio with media uploads
- Connect with fans and receive feedback
- Showcase artistic statements and genres

### 🎓 Educators
- Offer lessons and workshops
- Create educational events
- Manage student communications
- Share teaching resources

### ❤️ Fans
- Discover new artists and content
- Follow favorite artists
- RSVP to events and workshops
- Save and share content

### 👑 Admins
- Full platform management
- User and content moderation
- Analytics and reporting
- Site configuration

## 🛠️ Development Commands

```bash
# Development
npm run dev                    # Start development server
npm run build                  # Build for production
npm run preview               # Preview production build

# Firebase
npm run firebase:deploy       # Deploy everything to Firebase
npm run firebase:emulators    # Start Firebase emulators
npm run firebase:rules        # Deploy security rules
npm run firebase:indexes      # Deploy database indexes
```

## 🌐 Deployment

### Automatic Deployment
```bash
npm run firebase:deploy
```

### Manual Deployment
```bash
npm run build
firebase deploy
```

Your app will be available at: `https://musicart-7641d.web.app`

## 🔧 Configuration

### Firebase Project
- **Project ID**: `musicart-7641d`
- **Storage**: `gs://musicart-7641d.firebasestorage.app`
- **Console**: [Firebase Console](https://console.firebase.google.com/project/musicart-7641d)

### Environment Variables
```env
# Firebase Configuration
VITE_FIREBASE_API_KEY=your_api_key
VITE_FIREBASE_AUTH_DOMAIN=musicart-7641d.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=musicart-7641d
VITE_FIREBASE_STORAGE_BUCKET=musicart-7641d.firebasestorage.app
VITE_FIREBASE_MESSAGING_SENDER_ID=732159852081
VITE_FIREBASE_APP_ID=1:732159852081:web:4117b804da97bb8bc55f10
VITE_FIREBASE_MEASUREMENT_ID=G-38F2VTX3SN

# Optional: Gemini AI Integration
GEMINI_API_KEY=your_gemini_api_key
```
