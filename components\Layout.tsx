
import React, { ReactNode } from 'react';
import { Link, NavLink } from 'react-router-dom';
import { useFirebaseApp } from '../FirebaseAppContext';
import { UserRole, IconName } from '../types';
import { Icon } from '../components'; // Assuming Icon is in components.tsx

const Navbar: React.FC = () => {
  const { currentUser, logout } = useFirebaseApp();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = React.useState(false);

  // Path to your logo.
  // Assumes 'public' folder is at the web server's root, and 'logo.png' is inside it.
  const logoUrl = '/logo.png';

  const navItems = [
    { name: 'Home', path: '/', icon: 'home' as IconName },
    { name: 'Explore', path: '/explore', icon: 'bolt' as IconName },
    { name: 'Events', path: '/events', icon: 'calendar' as IconName },
    ...(currentUser ? [{
      name: 'Inbox',
      path: '/inbox',
      icon: 'inboxIn' as IconName,
      badge: currentUser.unreadMessageCount || 0
    }] : []),
    ...(currentUser?.role === UserRole.ADMIN ? [{ name: 'Admin', path: '/admin', icon: 'shield' as IconName }] : []),
  ];

  const NavItemLink: React.FC<{path: string, name: string, icon: IconName, onClick?: () => void, isMobile?: boolean, badge?: number}> = ({path, name, icon, onClick, isMobile, badge}) => (
    <NavLink
      to={path}
      onClick={onClick}
      className={({ isActive }) =>
        `flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors relative
         ${isActive
            ? (isMobile ? 'bg-primary text-white' : 'bg-primary-dark text-white') // Slightly different active for mobile for better visibility if needed
            : (isMobile ? 'text-neutral-lightest hover:bg-primary hover:text-white' : 'text-neutral-lightest hover:bg-primary-dark hover:text-white')
         }`
      }
    >
      <Icon name={icon} className="w-5 h-5 mr-2" />
      {name}
      {badge && badge > 0 && (
        <span className="ml-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
          {badge > 99 ? '99+' : badge}
        </span>
      )}
    </NavLink>
  );


  return (
    <nav className="bg-neutral-darkest shadow-lg sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-20"> {/* Increased height for logo */}
          <div className="flex items-center">
            <Link to="/" className="flex-shrink-0">
              <img src={logoUrl} alt="MusicArt Club Logo" className="h-14 w-auto" /> {/* Adjusted height */}
            </Link>
            <div className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-4">
                {navItems.map(item => <NavItemLink key={item.name} {...item} badge={item.badge} />)}
              </div>
            </div>
          </div>
          <div className="hidden md:block">
            <div className="ml-4 flex items-center md:ml-6">
              {currentUser ? (
                <>
                  <NavLink
                    to="/dashboard"
                    className={({ isActive }) =>
                      `flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors
                       ${isActive ? 'bg-primary-dark text-white' : 'text-neutral-lightest hover:bg-primary-dark hover:text-white'}`
                    }
                  >
                    <Icon name="userCircle" className="w-5 h-5 mr-2" />
                    My Dashboard
                  </NavLink>
                  <button
                    onClick={logout}
                    className="ml-3 flex items-center px-3 py-2 rounded-md text-sm font-medium text-neutral-lightest hover:bg-primary-dark hover:text-white transition-colors"
                  >
                    <Icon name="arrowLeftOnRectangle" className="w-5 h-5 mr-2" />
                    Logout
                  </button>
                </>
              ) : (
                <NavLink
                  to="/auth"
                  className={({ isActive }) =>
                    `flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors
                     ${isActive ? 'bg-primary-dark text-white' : 'text-neutral-lightest hover:bg-primary-dark hover:text-white'}`
                  }
                >
                  <Icon name="arrowRightOnRectangle" className="w-5 h-5 mr-2" />
                  Login / Sign Up
                </NavLink>
              )}
            </div>
          </div>
          <div className="-mr-2 flex md:hidden">
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              type="button"
              className="bg-neutral-darkest inline-flex items-center justify-center p-2 rounded-md text-primary hover:text-primary-light hover:bg-neutral-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-neutral-darkest focus:ring-white"
              aria-controls="mobile-menu"
              aria-expanded={isMobileMenuOpen}
            >
              <span className="sr-only">Open main menu</span>
              {isMobileMenuOpen ? (
                <Icon name="adjustmentsHorizontal" className="block h-6 w-6 transform rotate-45 text-white" />
              ) : (
                <Icon name="adjustmentsHorizontal" className="block h-6 w-6 text-white" />
              )}
            </button>
          </div>
        </div>
      </div>

      {isMobileMenuOpen && (
        <div className="md:hidden" id="mobile-menu">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
            {navItems.map(item => <NavItemLink key={item.name} {...item} badge={item.badge} isMobile onClick={() => setIsMobileMenuOpen(false)} />)}
          </div>
          <div className="pt-4 pb-3 border-t border-neutral-dark">
            {currentUser ? (
              <div className="px-2 space-y-1">
                 <NavItemLink path="/dashboard" name="My Dashboard" icon="userCircle" isMobile onClick={() => setIsMobileMenuOpen(false)} />
                <button
                  onClick={() => { logout(); setIsMobileMenuOpen(false); }}
                  className="w-full text-left flex items-center px-3 py-2 rounded-md text-base font-medium text-neutral-lightest hover:bg-primary hover:text-white transition-colors"
                >
                  <Icon name="arrowLeftOnRectangle" className="w-5 h-5 mr-2" />
                  Logout
                </button>
              </div>
            ) : (
              <div className="px-2 space-y-1">
                 <NavItemLink path="/auth" name="Login / Sign Up" icon="arrowRightOnRectangle" isMobile onClick={() => setIsMobileMenuOpen(false)} />
              </div>
            )}
          </div>
        </div>
      )}
    </nav>
  );
};

const Footer: React.FC = () => {
  return (
    <footer className="bg-neutral-darkest text-neutral-light mt-16">
      <div className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8 text-center">
        <p>&copy; {new Date().getFullYear()} The MusicArt Club. All rights reserved.</p>
        <p className="text-sm mt-1">A new creative community by musicart.ws</p>
      </div>
    </footer>
  );
};

export const PageContainer: React.FC<{ children: ReactNode; title?: string }> = ({ children, title }) => {
  if (title) {
    document.title = `${title} | MusicArt Club`;
  }
  return (
    <main className="flex-grow w-full">
      <div className="w-full max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {title && <h1 className="text-3xl font-bold text-neutral-darkest mb-6 text-center sm:text-left">{title}</h1>}
        <div className="w-full">
          {children}
        </div>
      </div>
    </main>
  );
};

export const Layout: React.FC<{ children: ReactNode }> = ({ children }) => {
  return (
    <div className="min-h-screen flex flex-col w-full overflow-x-hidden">
      <Navbar />
      <div className="flex-grow w-full">
        {children}
      </div>
      <Footer />
    </div>
  );
};
