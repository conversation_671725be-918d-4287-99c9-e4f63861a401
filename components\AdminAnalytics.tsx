import React, { useState } from 'react';
import { useFirebaseApp } from '../FirebaseAppContext';
import { ContentStats, UserRole, PostCategory } from '../types';
import { Button, Card, Icon } from '../components';

interface AdminAnalyticsProps {
  className?: string;
}

export const AdminAnalytics: React.FC<AdminAnalyticsProps> = ({ className = '' }) => {
  const { 
    users, posts, events, getUserStats, getContentStats, 
    exportUserData, exportContentData 
  } = useFirebaseApp();
  
  const [exportLoading, setExportLoading] = useState<'users' | 'content' | null>(null);
  
  const userStats = getUserStats();
  const contentStats = getContentStats();

  const handleExport = async (type: 'users' | 'content') => {
    setExportLoading(type);
    
    try {
      let data: string;
      let filename: string;
      
      if (type === 'users') {
        data = exportUserData();
        filename = `users-export-${new Date().toISOString().split('T')[0]}.json`;
      } else {
        data = exportContentData();
        filename = `content-export-${new Date().toISOString().split('T')[0]}.json`;
      }
      
      // Create and download file
      const blob = new Blob([data], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setExportLoading(null);
    }
  };

  // Calculate growth metrics
  const getGrowthMetrics = () => {
    const now = new Date();
    const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const lastMonth = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    const newUsersThisWeek = users.filter(user => 
      user.createdAt && new Date(user.createdAt) > lastWeek
    ).length;

    const newUsersThisMonth = users.filter(user => 
      user.createdAt && new Date(user.createdAt) > lastMonth
    ).length;

    const postsThisWeek = posts.filter(post => 
      new Date(post.createdAt) > lastWeek
    ).length;

    const eventsThisWeek = events.filter(event => 
      event.createdAt && new Date(event.createdAt) > lastWeek
    ).length;

    return {
      newUsersThisWeek,
      newUsersThisMonth,
      postsThisWeek,
      eventsThisWeek
    };
  };

  const growthMetrics = getGrowthMetrics();

  // Calculate engagement metrics
  const getEngagementMetrics = () => {
    const totalLikes = posts.reduce((sum, post) => sum + post.likes, 0);
    const totalRSVPs = events.reduce((sum, event) => sum + event.rsvps.length, 0);
    const avgLikesPerPost = posts.length > 0 ? (totalLikes / posts.length).toFixed(1) : '0';
    const avgRSVPsPerEvent = events.length > 0 ? (totalRSVPs / events.length).toFixed(1) : '0';

    return {
      totalLikes,
      totalRSVPs,
      avgLikesPerPost,
      avgRSVPsPerEvent
    };
  };

  const engagementMetrics = getEngagementMetrics();

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="p-6 text-center">
          <Icon name="users" className="w-12 h-12 text-primary mx-auto mb-2" />
          <h3 className="text-2xl font-bold text-neutral-darkest">{userStats.totalUsers}</h3>
          <p className="text-neutral-dark">Total Users</p>
          <p className="text-sm text-green-600 mt-1">+{growthMetrics.newUsersThisWeek} this week</p>
        </Card>
        
        <Card className="p-6 text-center">
          <Icon name="photo" className="w-12 h-12 text-blue-500 mx-auto mb-2" />
          <h3 className="text-2xl font-bold text-neutral-darkest">{posts.length}</h3>
          <p className="text-neutral-dark">Total Posts</p>
          <p className="text-sm text-green-600 mt-1">+{growthMetrics.postsThisWeek} this week</p>
        </Card>
        
        <Card className="p-6 text-center">
          <Icon name="calendar" className="w-12 h-12 text-purple-500 mx-auto mb-2" />
          <h3 className="text-2xl font-bold text-neutral-darkest">{events.length}</h3>
          <p className="text-neutral-dark">Total Events</p>
          <p className="text-sm text-green-600 mt-1">+{growthMetrics.eventsThisWeek} this week</p>
        </Card>
        
        <Card className="p-6 text-center">
          <Icon name="heart" className="w-12 h-12 text-red-500 mx-auto mb-2" />
          <h3 className="text-2xl font-bold text-neutral-darkest">{engagementMetrics.totalLikes}</h3>
          <p className="text-neutral-dark">Total Likes</p>
          <p className="text-sm text-neutral-dark mt-1">{engagementMetrics.avgLikesPerPost} avg per post</p>
        </Card>
      </div>

      {/* User Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-xl font-semibold text-neutral-darkest mb-4 flex items-center">
            <Icon name="chartPie" className="w-6 h-6 mr-2" />
            User Distribution
          </h3>
          <div className="space-y-4">
            {Object.entries(userStats.usersByRole).map(([role, count]) => {
              const percentage = ((count / userStats.totalUsers) * 100).toFixed(1);
              return (
                <div key={role} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className={`w-4 h-4 rounded mr-3 ${
                      role === UserRole.ARTIST ? 'bg-primary' :
                      role === UserRole.EDUCATOR ? 'bg-secondary' :
                      role === UserRole.FAN ? 'bg-accent' : 'bg-neutral-dark'
                    }`}></div>
                    <span className="text-neutral-darkest font-medium">{role}s</span>
                  </div>
                  <div className="text-right">
                    <span className="text-lg font-bold text-neutral-darkest">{count}</span>
                    <span className="text-sm text-neutral-dark ml-2">({percentage}%)</span>
                  </div>
                </div>
              );
            })}
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-xl font-semibold text-neutral-darkest mb-4 flex items-center">
            <Icon name="trendingUp" className="w-6 h-6 mr-2" />
            Growth Metrics
          </h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-neutral-dark">New Users (Week)</span>
              <span className="text-lg font-bold text-green-600">+{growthMetrics.newUsersThisWeek}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-neutral-dark">New Users (Month)</span>
              <span className="text-lg font-bold text-green-600">+{growthMetrics.newUsersThisMonth}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-neutral-dark">Active Users</span>
              <span className="text-lg font-bold text-primary">{userStats.activeUsers}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-neutral-dark">User Retention</span>
              <span className="text-lg font-bold text-blue-600">
                {((userStats.activeUsers / userStats.totalUsers) * 100).toFixed(1)}%
              </span>
            </div>
          </div>
        </Card>
      </div>

      {/* Content Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-xl font-semibold text-neutral-darkest mb-4 flex items-center">
            <Icon name="chartBar" className="w-6 h-6 mr-2" />
            Content Categories
          </h3>
          <div className="space-y-3">
            {Object.values(PostCategory).map(category => {
              const count = posts.filter(post => post.category === category).length;
              const percentage = posts.length > 0 ? ((count / posts.length) * 100).toFixed(1) : '0';
              return (
                <div key={category} className="flex justify-between items-center">
                  <span className="text-neutral-dark">{category}</span>
                  <div className="text-right">
                    <span className="text-lg font-bold text-neutral-darkest">{count}</span>
                    <span className="text-sm text-neutral-dark ml-2">({percentage}%)</span>
                  </div>
                </div>
              );
            })}
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-xl font-semibold text-neutral-darkest mb-4 flex items-center">
            <Icon name="fire" className="w-6 h-6 mr-2" />
            Engagement Stats
          </h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-neutral-dark">Total Likes</span>
              <span className="text-lg font-bold text-red-500">{engagementMetrics.totalLikes}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-neutral-dark">Avg Likes/Post</span>
              <span className="text-lg font-bold text-red-500">{engagementMetrics.avgLikesPerPost}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-neutral-dark">Total RSVPs</span>
              <span className="text-lg font-bold text-purple-500">{engagementMetrics.totalRSVPs}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-neutral-dark">Avg RSVPs/Event</span>
              <span className="text-lg font-bold text-purple-500">{engagementMetrics.avgRSVPsPerEvent}</span>
            </div>
          </div>
        </Card>
      </div>

      {/* Export Section */}
      <Card className="p-6">
        <h3 className="text-xl font-semibold text-neutral-darkest mb-4 flex items-center">
          <Icon name="arrowDownTray" className="w-6 h-6 mr-2" />
          Data Export
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-4 border border-neutral-light rounded-lg">
            <h4 className="font-medium text-neutral-darkest mb-2">User Data Export</h4>
            <p className="text-sm text-neutral-dark mb-4">
              Export all user data including profiles, roles, and activity information.
            </p>
            <Button
              onClick={() => handleExport('users')}
              isLoading={exportLoading === 'users'}
              variant="outline"
              className="w-full"
            >
              <Icon name="users" className="w-4 h-4 mr-2" />
              Export Users
            </Button>
          </div>
          
          <div className="p-4 border border-neutral-light rounded-lg">
            <h4 className="font-medium text-neutral-darkest mb-2">Content Data Export</h4>
            <p className="text-sm text-neutral-dark mb-4">
              Export all posts, events, and related engagement data.
            </p>
            <Button
              onClick={() => handleExport('content')}
              isLoading={exportLoading === 'content'}
              variant="outline"
              className="w-full"
            >
              <Icon name="documentText" className="w-4 h-4 mr-2" />
              Export Content
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};
