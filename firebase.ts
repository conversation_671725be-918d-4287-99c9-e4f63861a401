// Firebase configuration and initialization
import { initializeApp } from "firebase/app";
import { getAnalytics } from "firebase/analytics";
import { getAuth, connectAuthEmulator } from "firebase/auth";
import { getFirestore, connectFirestoreEmulator } from "firebase/firestore";
import { getStorage, connectStorageEmulator } from "firebase/storage";

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyBhVCh-UFBHIYdnLQ0C37GHFSCGpmQOHc8",
  authDomain: "musicart-7641d.firebaseapp.com",
  projectId: "musicart-7641d",
  storageBucket: "musicart-7641d.firebasestorage.app",
  messagingSenderId: "732159852081",
  appId: "1:732159852081:web:4117b804da97bb8bc55f10",
  measurementId: "G-38F2VTX3SN"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);
export const analytics = getAnalytics(app);

// Using production Firebase - no emulators

export default app;
