import React, { useState, useEffect } from 'react';
import { PageContainer } from '../components/Layout';
import { useApp } from '../AppContext';
import { Message, User } from '../types';
import { Button, Input, Textarea, Card, Avatar, Icon, Modal, Tabs } from '../components';

const InboxPage: React.FC = () => {
  const { 
    currentUser, 
    getMessages, 
    sendMessage, 
    markMessageAsRead, 
    deleteMessage, 
    getUserById,
    users 
  } = useApp();
  
  const [messages, setMessages] = useState<{ sent: Message[]; received: Message[] }>({ sent: [], received: [] });
  const [isComposeModalOpen, setIsComposeModalOpen] = useState(false);
  const [selectedMessage, setSelectedMessage] = useState<Message | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  
  const [newMessage, setNewMessage] = useState({
    receiverId: '',
    subject: '',
    content: ''
  });

  useEffect(() => {
    if (currentUser) {
      setMessages(getMessages(currentUser.id));
    }
  }, [currentUser, getMessages]);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentUser || !newMessage.receiverId || !newMessage.subject || !newMessage.content) {
      alert('Please fill in all fields');
      return;
    }

    const result = await sendMessage({
      senderId: currentUser.id,
      receiverId: newMessage.receiverId,
      subject: newMessage.subject,
      content: newMessage.content
    });

    if (result) {
      setMessages(getMessages(currentUser.id));
      setNewMessage({ receiverId: '', subject: '', content: '' });
      setIsComposeModalOpen(false);
    }
  };

  const handleViewMessage = async (message: Message) => {
    setSelectedMessage(message);
    setIsViewModalOpen(true);
    
    // Mark as read if it's a received message
    if (message.receiverId === currentUser?.id && !message.isRead) {
      await markMessageAsRead(message.id);
      setMessages(getMessages(currentUser.id));
    }
  };

  const handleDeleteMessage = async (messageId: string) => {
    if (confirm('Are you sure you want to delete this message?')) {
      await deleteMessage(messageId);
      setMessages(getMessages(currentUser?.id || ''));
      setIsViewModalOpen(false);
    }
  };

  const MessageList: React.FC<{ messages: Message[]; type: 'sent' | 'received' }> = ({ messages, type }) => (
    <div className="space-y-4">
      {messages.length === 0 ? (
        <div className="text-center py-8 text-neutral-dark">
          <Icon name="envelope" className="w-12 h-12 mx-auto mb-4 text-neutral-light" />
          <p>No {type} messages</p>
        </div>
      ) : (
        messages.map((message) => {
          const otherUser = getUserById(type === 'sent' ? message.receiverId : message.senderId);
          return (
            <Card 
              key={message.id} 
              className={`p-4 cursor-pointer hover:shadow-md transition-shadow ${
                type === 'received' && !message.isRead ? 'border-l-4 border-l-primary bg-blue-50' : ''
              }`}
              onClick={() => handleViewMessage(message)}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3 flex-1">
                  <Avatar 
                    src={otherUser?.profilePhotoUrl} 
                    alt={otherUser?.displayName} 
                    size="sm" 
                  />
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <h4 className="font-medium text-neutral-darkest truncate">
                        {type === 'sent' ? `To: ${otherUser?.displayName}` : `From: ${otherUser?.displayName}`}
                      </h4>
                      {type === 'received' && !message.isRead && (
                        <span className="bg-primary text-white text-xs px-2 py-1 rounded-full">New</span>
                      )}
                    </div>
                    <p className="text-sm font-medium text-neutral-darkest truncate">{message.subject}</p>
                    <p className="text-sm text-neutral-dark truncate">{message.content}</p>
                    <p className="text-xs text-neutral-dark mt-1">
                      {new Date(message.createdAt).toLocaleDateString()} at {new Date(message.createdAt).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                    </p>
                  </div>
                </div>
                <Icon name="chevronRight" className="w-5 h-5 text-neutral-light" />
              </div>
            </Card>
          );
        })
      )}
    </div>
  );

  const tabs = [
    {
      name: `Inbox (${messages.received.length})`,
      content: <MessageList messages={messages.received} type="received" />
    },
    {
      name: `Sent (${messages.sent.length})`,
      content: <MessageList messages={messages.sent} type="sent" />
    }
  ];

  return (
    <PageContainer title="Inbox">
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-neutral-darkest">Messages</h1>
          <p className="text-neutral-dark">Communicate with other community members</p>
        </div>
        <Button onClick={() => setIsComposeModalOpen(true)} leftIcon={<Icon name="pencil" />}>
          Compose Message
        </Button>
      </div>

      <Tabs tabs={tabs} />

      {/* Compose Message Modal */}
      <Modal 
        isOpen={isComposeModalOpen} 
        onClose={() => setIsComposeModalOpen(false)} 
        title="Compose Message"
      >
        <form onSubmit={handleSendMessage} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-neutral-darkest mb-2">
              To:
            </label>
            <select
              value={newMessage.receiverId}
              onChange={(e) => setNewMessage(prev => ({ ...prev, receiverId: e.target.value }))}
              className="w-full px-3 py-2 border border-neutral-light rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              required
            >
              <option value="">Select recipient...</option>
              {users
                .filter(user => user.id !== currentUser?.id)
                .map(user => (
                  <option key={user.id} value={user.id}>
                    {user.displayName} ({user.role})
                  </option>
                ))
              }
            </select>
          </div>
          
          <Input
            label="Subject"
            value={newMessage.subject}
            onChange={(e) => setNewMessage(prev => ({ ...prev, subject: e.target.value }))}
            required
          />
          
          <Textarea
            label="Message"
            value={newMessage.content}
            onChange={(e) => setNewMessage(prev => ({ ...prev, content: e.target.value }))}
            rows={6}
            required
          />
          
          <div className="flex justify-end space-x-3">
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => setIsComposeModalOpen(false)}
            >
              Cancel
            </Button>
            <Button type="submit">
              Send Message
            </Button>
          </div>
        </form>
      </Modal>

      {/* View Message Modal */}
      <Modal 
        isOpen={isViewModalOpen} 
        onClose={() => setIsViewModalOpen(false)} 
        title="Message"
      >
        {selectedMessage && (
          <div className="space-y-4">
            <div className="border-b border-neutral-light pb-4">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-lg font-semibold text-neutral-darkest">
                  {selectedMessage.subject}
                </h3>
                <Button
                  size="sm"
                  variant="danger"
                  onClick={() => handleDeleteMessage(selectedMessage.id)}
                >
                  <Icon name="trash" className="w-4 h-4" />
                </Button>
              </div>
              <div className="flex items-center space-x-3">
                <Avatar 
                  src={getUserById(selectedMessage.senderId)?.profilePhotoUrl} 
                  alt={getUserById(selectedMessage.senderId)?.displayName} 
                  size="sm" 
                />
                <div>
                  <p className="text-sm font-medium text-neutral-darkest">
                    From: {getUserById(selectedMessage.senderId)?.displayName}
                  </p>
                  <p className="text-xs text-neutral-dark">
                    {new Date(selectedMessage.createdAt).toLocaleDateString()} at {new Date(selectedMessage.createdAt).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                  </p>
                </div>
              </div>
            </div>
            
            <div className="whitespace-pre-wrap text-neutral-darkest">
              {selectedMessage.content}
            </div>
            
            <div className="flex justify-end space-x-3 pt-4 border-t border-neutral-light">
              <Button 
                variant="outline" 
                onClick={() => {
                  setNewMessage({
                    receiverId: selectedMessage.senderId,
                    subject: `Re: ${selectedMessage.subject}`,
                    content: ''
                  });
                  setIsViewModalOpen(false);
                  setIsComposeModalOpen(true);
                }}
              >
                Reply
              </Button>
              <Button onClick={() => setIsViewModalOpen(false)}>
                Close
              </Button>
            </div>
          </div>
        )}
      </Modal>
    </PageContainer>
  );
};

export default InboxPage;
