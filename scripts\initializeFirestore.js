#!/usr/bin/env node

// Initialize Firestore collections for MusicArt Club
import { initializeApp } from 'firebase/app';
import { getFirestore, collection, doc, setDoc, serverTimestamp } from 'firebase/firestore';
import { getAuth, createUserWithEmailAndPassword, updateProfile } from 'firebase/auth';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyBhVCh-UFBHIYdnLQ0C37GHFSCGpmQOHc8",
  authDomain: "musicart-7641d.firebaseapp.com",
  projectId: "musicart-7641d",
  storageBucket: "musicart-7641d.firebasestorage.app",
  messagingSenderId: "732159852081",
  appId: "1:732159852081:web:4117b804da97bb8bc55f10",
  measurementId: "G-38F2VTX3SN"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);
const auth = getAuth(app);

// Collection names
const COLLECTIONS = {
  USERS: 'users',
  POSTS: 'posts',
  EVENTS: 'events',
  LESSONS: 'lessons',
  MESSAGES: 'messages',
  COMMENTS: 'comments',
  SITE_SETTINGS: 'siteSettings',
  ANNOUNCEMENTS: 'announcements'
};

async function initializeCollections() {
  console.log('🚀 Initializing Firestore collections...');

  try {
    // 1. Create admin user first
    console.log('👑 Creating admin user...');
    let adminUserId;
    try {
      const adminCredential = await createUserWithEmailAndPassword(
        auth, 
        '<EMAIL>', 
        'MusicArt2024!'
      );
      
      await updateProfile(adminCredential.user, {
        displayName: 'Admin User'
      });
      
      adminUserId = adminCredential.user.uid;
      console.log('✅ Admin user created with ID:', adminUserId);
    } catch (error) {
      if (error.code === 'auth/email-already-in-use') {
        console.log('📧 Admin email already exists, continuing...');
        // We'll create a dummy admin doc with a known ID
        adminUserId = 'admin-user-id';
      } else {
        throw error;
      }
    }

    // 2. Initialize users collection with admin
    console.log('👥 Initializing users collection...');
    await setDoc(doc(db, COLLECTIONS.USERS, adminUserId), {
      email: '<EMAIL>',
      role: 'Admin',
      displayName: 'Admin User',
      profilePhotoUrl: 'https://picsum.photos/seed/admin/200/200',
      bannerImageUrl: 'https://picsum.photos/seed/admin-banner/1200/400',
      bio: 'Platform administrator with full access to all features and settings.',
      tags: ['Administrator', 'Platform Manager'],
      socialLinks: {},
      isActive: true,
      createdAt: serverTimestamp(),
      lastLoginAt: serverTimestamp(),
      unreadMessageCount: 0
    });

    // 3. Initialize site settings
    console.log('⚙️ Initializing site settings...');
    await setDoc(doc(db, COLLECTIONS.SITE_SETTINGS, 'main'), {
      siteName: 'The MusicArt Club',
      siteDescription: 'A vibrant creative community where music and art enthusiasts connect, share, and grow together.',
      contactEmail: '<EMAIL>',
      maintenanceMode: false,
      allowRegistration: true,
      featuredPostsLimit: 5,
      theme: {
        primaryColor: '#f97316',
        secondaryColor: '#8b5cf6',
        accentColor: '#06b6d4'
      },
      updatedAt: serverTimestamp()
    });

    // 4. Create welcome announcement
    console.log('📢 Creating welcome announcement...');
    await setDoc(doc(db, COLLECTIONS.ANNOUNCEMENTS, 'welcome'), {
      title: 'Welcome to The MusicArt Club!',
      message: 'We\'re excited to have you join our creative community. Explore, create, and connect with fellow artists and music lovers.',
      type: 'success',
      isActive: true,
      targetRoles: [],
      createdAt: serverTimestamp()
    });

    // 5. Create empty collections with placeholder docs (will be auto-deleted when real data is added)
    console.log('📝 Creating collection placeholders...');
    
    // Posts collection
    await setDoc(doc(db, COLLECTIONS.POSTS, '_placeholder'), {
      _placeholder: true,
      createdAt: serverTimestamp()
    });

    // Events collection
    await setDoc(doc(db, COLLECTIONS.EVENTS, '_placeholder'), {
      _placeholder: true,
      createdAt: serverTimestamp()
    });

    // Lessons collection
    await setDoc(doc(db, COLLECTIONS.LESSONS, '_placeholder'), {
      _placeholder: true,
      createdAt: serverTimestamp()
    });

    // Messages collection
    await setDoc(doc(db, COLLECTIONS.MESSAGES, '_placeholder'), {
      _placeholder: true,
      createdAt: serverTimestamp()
    });

    // Comments collection
    await setDoc(doc(db, COLLECTIONS.COMMENTS, '_placeholder'), {
      _placeholder: true,
      createdAt: serverTimestamp()
    });

    console.log('✅ All collections initialized successfully!');
    console.log('');
    console.log('📋 Collections created:');
    console.log('  • users (with admin user)');
    console.log('  • posts');
    console.log('  • events');
    console.log('  • lessons');
    console.log('  • messages');
    console.log('  • comments');
    console.log('  • siteSettings (configured)');
    console.log('  • announcements (with welcome message)');
    console.log('');
    console.log('🔑 Admin credentials:');
    console.log('  Email: <EMAIL>');
    console.log('  Password: MusicArt2024!');
    console.log('');
    console.log('🎉 Your Firestore database is ready!');

  } catch (error) {
    console.error('❌ Error initializing collections:', error);
    process.exit(1);
  }
}

// Run the initialization
initializeCollections()
  .then(() => {
    console.log('🏁 Initialization complete!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Initialization failed:', error);
    process.exit(1);
  });
