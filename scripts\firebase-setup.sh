#!/bin/bash

# Firebase Setup Script for MusicArt Club
# This script automates the Firebase initialization and deployment process

echo "🔥 Firebase Setup for MusicArt Club 🔥"
echo "======================================="

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI not found. Installing..."
    npm install -g firebase-tools
else
    echo "✅ Firebase CLI is installed"
fi

# Login to Firebase (if not already logged in)
echo "🔐 Checking Firebase authentication..."
if ! firebase projects:list &> /dev/null; then
    echo "Please log in to Firebase:"
    firebase login
else
    echo "✅ Already logged in to Firebase"
fi

# Initialize Firebase project (if not already initialized)
if [ ! -f "firebase.json" ]; then
    echo "🚀 Initializing Firebase project..."
    firebase init
else
    echo "✅ Firebase project already initialized"
fi

# Deploy Firestore rules and indexes
echo "📋 Deploying Firestore rules and indexes..."
firebase deploy --only firestore:rules,firestore:indexes

# Deploy Storage rules
echo "💾 Deploying Storage rules..."
firebase deploy --only storage

# Build the project
echo "🔨 Building the project..."
npm run build

# Deploy to Firebase Hosting
echo "🌐 Deploying to Firebase Hosting..."
firebase deploy --only hosting

echo ""
echo "🎉 Firebase setup and deployment complete!"
echo ""
echo "Your app should now be available at:"
echo "https://musicart-7641d.web.app"
echo ""
echo "Firebase Console: https://console.firebase.google.com/project/musicart-7641d"
echo ""
echo "Next steps:"
echo "1. Set up authentication providers in Firebase Console"
echo "2. Configure custom domain (optional)"
echo "3. Set up monitoring and analytics"
echo "4. Configure backup schedules"
