import { AuthService, UserService } from './firebaseService';
import { UserRole } from '../types';

export class AdminSeederService {
  private static readonly DEFAULT_ADMIN_EMAIL = '<EMAIL>';
  private static readonly DEFAULT_ADMIN_PASSWORD = 'MusicArt2024!';
  private static readonly DEFAULT_ADMIN_NAME = 'Admin User';

  /**
   * Check if an admin user exists in the system
   */
  static async adminExists(): Promise<boolean> {
    try {
      const users = await UserService.getAllUsers();
      return users.some(user => user.role === UserRole.ADMIN);
    } catch (error) {
      console.error('Error checking for admin user:', error);
      return false;
    }
  }

  /**
   * Create a default admin user if none exists
   */
  static async ensureAdminExists(): Promise<void> {
    try {
      const adminExists = await this.adminExists();
      
      if (adminExists) {
        console.log('✅ Admin user already exists');
        return;
      }

      console.log('🔧 Creating default admin user...');
      
      const adminData = {
        email: this.DEFAULT_ADMIN_EMAIL,
        password: this.DEFAULT_ADMIN_PASSWORD,
        displayName: this.DEFAULT_ADMIN_NAME,
        role: UserRole.ADMIN,
        bio: 'Platform administrator with full access to all features and settings.',
        tags: ['Administrator', 'Platform Manager'],
        socialLinks: {}
      };

      const adminUser = await AuthService.signUp(
        adminData.email,
        adminData.password,
        adminData
      );

      if (adminUser) {
        console.log('✅ Default admin user created successfully');
        console.log(`📧 Email: ${this.DEFAULT_ADMIN_EMAIL}`);
        console.log(`🔑 Password: ${this.DEFAULT_ADMIN_PASSWORD}`);
        console.log('⚠️  Please change the admin password after first login!');
      } else {
        console.error('❌ Failed to create admin user');
      }
    } catch (error) {
      console.error('Error creating admin user:', error);
      
      // If the error is because the email already exists in Firebase Auth
      // but not in our Firestore collection, we should handle this case
      if (error instanceof Error && error.message.includes('email-already-in-use')) {
        console.log('📧 Admin email already exists in Firebase Auth');
        console.log('🔑 You can sign in with existing credentials');
      }
    }
  }

  /**
   * Get admin credentials for display (development only)
   */
  static getAdminCredentials() {
    return {
      email: this.DEFAULT_ADMIN_EMAIL,
      password: this.DEFAULT_ADMIN_PASSWORD,
      displayName: this.DEFAULT_ADMIN_NAME
    };
  }
}
