import { AuthService, UserService } from './firebaseService';
import { UserRole } from '../types';

export class AdminSeederService {
  private static readonly DEFAULT_ADMIN_EMAIL = '<EMAIL>';
  private static readonly DEFAULT_ADMIN_PASSWORD = 'MusicArt2024!';
  private static readonly DEFAULT_ADMIN_NAME = 'Admin User';

  /**
   * Check if an admin user exists in the system
   */
  static async adminExists(): Promise<boolean> {
    try {
      console.log('🔍 Checking for existing admin users...');
      const users = await UserService.getAllUsers();
      console.log(`📊 Found ${users.length} total users`);
      const hasAdmin = users.some(user => user.role === UserRole.ADMIN);
      console.log(`👑 Admin exists: ${hasAdmin}`);
      return hasAdmin;
    } catch (error) {
      console.error('❌ Error checking for admin user:', error);
      return false;
    }
  }

  /**
   * Create a default admin user if none exists
   */
  static async ensureAdminExists(): Promise<void> {
    try {
      console.log('🚀 Starting admin user check...');
      const adminExists = await this.adminExists();

      if (adminExists) {
        console.log('✅ Admin user already exists, skipping creation');
        return;
      }

      console.log('🔧 No admin found, creating default admin user...');

      const adminData = {
        email: this.DEFAULT_ADMIN_EMAIL,
        password: this.DEFAULT_ADMIN_PASSWORD,
        displayName: this.DEFAULT_ADMIN_NAME,
        role: UserRole.ADMIN,
        bio: 'Platform administrator with full access to all features and settings.',
        tags: ['Administrator', 'Platform Manager'],
        socialLinks: {}
      };

      console.log(`📝 Creating admin with email: ${adminData.email}`);
      const adminUser = await AuthService.signUp(
        adminData.email,
        adminData.password,
        adminData
      );

      if (adminUser) {
        console.log('✅ Default admin user created successfully');
        console.log(`📧 Email: ${this.DEFAULT_ADMIN_EMAIL}`);
        console.log(`🔑 Password: ${this.DEFAULT_ADMIN_PASSWORD}`);
        console.log('⚠️  Please change the admin password after first login!');
      } else {
        console.error('❌ Failed to create admin user - AuthService.signUp returned null');
      }
    } catch (error) {
      console.error('❌ Error creating admin user:', error);

      // If the error is because the email already exists in Firebase Auth
      // but not in our Firestore collection, we should handle this case
      if (error instanceof Error && error.message.includes('email-already-in-use')) {
        console.log('📧 Admin email already exists in Firebase Auth');
        console.log('🔑 You can sign in with existing credentials');
      }

      // Don't throw the error - let the app continue to load
      console.log('⚠️  Continuing app initialization despite admin creation error');
    }
  }

  /**
   * Get admin credentials for display (development only)
   */
  static getAdminCredentials() {
    return {
      email: this.DEFAULT_ADMIN_EMAIL,
      password: this.DEFAULT_ADMIN_PASSWORD,
      displayName: this.DEFAULT_ADMIN_NAME
    };
  }
}
