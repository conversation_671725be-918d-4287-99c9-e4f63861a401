import React, { useState } from 'react';
import { AdminSeederService } from '../services/adminSeeder';

interface AdminInfoProps {
  className?: string;
}

const AdminInfo: React.FC<AdminInfoProps> = ({ className = '' }) => {
  const [showCredentials, setShowCredentials] = useState(false);
  const adminCredentials = AdminSeederService.getAdminCredentials();

  return (
    <div className={`bg-gradient-to-br from-red-50 to-orange-50 border border-red-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
            <span className="text-red-600 text-sm">🛡️</span>
          </div>
        </div>
        <div className="flex-1">
          <h3 className="text-sm font-medium text-red-800 mb-1">
            Admin Account Information
          </h3>
          <p className="text-xs text-red-700 mb-3">
            A default admin account has been created for you to manage the platform.
          </p>
          
          <button
            onClick={() => setShowCredentials(!showCredentials)}
            className="text-xs bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded transition-colors"
          >
            {showCredentials ? 'Hide' : 'Show'} Admin Credentials
          </button>
          
          {showCredentials && (
            <div className="mt-3 p-3 bg-white/70 rounded border border-red-200">
              <div className="space-y-2 text-xs">
                <div>
                  <span className="font-medium text-red-800">Email:</span>
                  <span className="ml-2 font-mono text-red-700">{adminCredentials.email}</span>
                </div>
                <div>
                  <span className="font-medium text-red-800">Password:</span>
                  <span className="ml-2 font-mono text-red-700">{adminCredentials.password}</span>
                </div>
              </div>
              <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded">
                <p className="text-xs text-yellow-800">
                  ⚠️ <strong>Important:</strong> Please change the admin password after your first login for security.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdminInfo;
