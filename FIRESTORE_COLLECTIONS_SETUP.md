# Firestore Collections Setup

## Required Collections for MusicArt Club

Your app needs these 8 Firestore collections:

### 1. **users** 
- Stores user profiles and authentication data
- Fields: email, role, displayName, profilePhotoUrl, bio, tags, etc.

### 2. **posts**
- Artist posts and media content
- Fields: title, content, category, userId, mediaUrls, likes, etc.

### 3. **events**
- Educator events and workshops
- Fields: title, description, dateTime, educatorId, location, etc.

### 4. **lessons**
- Educator lesson offerings
- Fields: title, description, price, duration, educatorId, etc.

### 5. **messages**
- Direct messages between users
- Fields: senderId, receiverId, content, isRead, etc.

### 6. **comments**
- Comments on posts
- Fields: postId, userId, content, createdAt, etc.

### 7. **siteSettings**
- Global application settings
- Fields: siteName, siteDescription, theme, maintenanceMode, etc.

### 8. **announcements**
- Admin announcements
- Fields: title, message, type, isActive, targetRoles, etc.

## How Collections Will Be Created

**GOOD NEWS**: You don't need to manually create these collections!

When you run your app and it connects to Firebase:

1. **Automatic Creation**: Firestore collections are created automatically when the first document is written to them
2. **Admin User**: The app will try to create an admin user, which creates the `users` collection
3. **Site Settings**: The app will create default site settings, which creates the `siteSettings` collection
4. **Welcome Announcement**: The app will create a welcome message, which creates the `announcements` collection
5. **Other Collections**: Will be created when users start using the app (posting, messaging, etc.)

## Manual Creation (If Needed)

If the automatic creation fails, you can manually create collections in the Firebase Console:

1. Go to [Firebase Console](https://console.firebase.google.com/project/musicart-7641d/firestore)
2. Click "Start collection"
3. Enter collection name (e.g., "users")
4. Add a temporary document with any field
5. The collection will be created
6. You can delete the temporary document later

## Admin User Creation

The app will automatically try to create an admin user with:
- **Email**: <EMAIL>
- **Password**: MusicArt2024!
- **Role**: Admin

## Security Rules

Your Firestore security rules are already deployed and will:
- Allow authenticated users to read/write their own data
- Allow admins to manage all data
- Protect sensitive operations

## Indexes

All required database indexes are already deployed for optimal query performance.

## Next Steps

1. **Start your app**: `npm run dev`
2. **Let it initialize**: The app will create collections automatically
3. **Check Firebase Console**: Verify collections are created
4. **Login as admin**: Use the admin credentials to test functionality

The collections will be created automatically when you use the app!
