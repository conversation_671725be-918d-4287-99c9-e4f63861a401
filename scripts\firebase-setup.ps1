# Firebase Setup Script for MusicArt Club (PowerShell)
# This script automates the Firebase initialization and deployment process

Write-Host "🔥 Firebase Setup for MusicArt Club 🔥" -ForegroundColor Yellow
Write-Host "=======================================" -ForegroundColor Yellow

# Check if Firebase CLI is installed
try {
    firebase --version | Out-Null
    Write-Host "✅ Firebase CLI is installed" -ForegroundColor Green
} catch {
    Write-Host "❌ Firebase CLI not found. Installing..." -ForegroundColor Red
    npm install -g firebase-tools
}

# Login to Firebase (if not already logged in)
Write-Host "🔐 Checking Firebase authentication..." -ForegroundColor Cyan
try {
    firebase projects:list | Out-Null
    Write-Host "✅ Already logged in to Firebase" -ForegroundColor Green
} catch {
    Write-Host "Please log in to Firebase:" -ForegroundColor Yellow
    firebase login
}

# Initialize Firebase project (if not already initialized)
if (-not (Test-Path "firebase.json")) {
    Write-Host "🚀 Initializing Firebase project..." -ForegroundColor Cyan
    firebase init
} else {
    Write-Host "✅ Firebase project already initialized" -ForegroundColor Green
}

# Deploy Firestore rules and indexes
Write-Host "📋 Deploying Firestore rules and indexes..." -ForegroundColor Cyan
firebase deploy --only firestore:rules,firestore:indexes

# Deploy Storage rules
Write-Host "💾 Deploying Storage rules..." -ForegroundColor Cyan
firebase deploy --only storage

# Build the project
Write-Host "🔨 Building the project..." -ForegroundColor Cyan
npm run build

# Deploy to Firebase Hosting
Write-Host "🌐 Deploying to Firebase Hosting..." -ForegroundColor Cyan
firebase deploy --only hosting

Write-Host ""
Write-Host "🎉 Firebase setup and deployment complete!" -ForegroundColor Green
Write-Host ""
Write-Host "Your app should now be available at:" -ForegroundColor Yellow
Write-Host "https://musicart-7641d.web.app" -ForegroundColor Blue
Write-Host ""
Write-Host "Firebase Console: https://console.firebase.google.com/project/musicart-7641d" -ForegroundColor Blue
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Set up authentication providers in Firebase Console"
Write-Host "2. Configure custom domain (optional)"
Write-Host "3. Set up monitoring and analytics"
Write-Host "4. Configure backup schedules"
