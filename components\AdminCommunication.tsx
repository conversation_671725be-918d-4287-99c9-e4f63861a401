import React, { useState } from 'react';
import { useFirebaseApp } from '../FirebaseAppContext';
import { Announcement, UserRole } from '../types';
import { Button, Input, Select, Card, Icon } from '../components';

interface AdminCommunicationProps {
  className?: string;
}

export const AdminCommunication: React.FC<AdminCommunicationProps> = ({ className = '' }) => {
  const { 
    getAnnouncements, createAnnouncement, updateAnnouncement, deleteAnnouncement 
  } = useFirebaseApp();
  
  const [announcements] = useState<Announcement[]>(getAnnouncements());
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  const [newAnnouncement, setNewAnnouncement] = useState({
    title: '',
    message: '',
    type: 'info' as Announcement['type'],
    targetRoles: [] as UserRole[],
    isActive: true,
    expiresAt: ''
  });

  const handleCreateAnnouncement = async () => {
    if (!newAnnouncement.title || !newAnnouncement.message) return;
    
    setIsLoading(true);
    try {
      const announcement = await createAnnouncement({
        ...newAnnouncement,
        expiresAt: newAnnouncement.expiresAt || undefined
      });
      
      if (announcement) {
        setNewAnnouncement({
          title: '',
          message: '',
          type: 'info',
          targetRoles: [],
          isActive: true,
          expiresAt: ''
        });
        setShowCreateForm(false);
      }
    } catch (error) {
      console.error('Failed to create announcement:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleAnnouncement = async (id: string, isActive: boolean) => {
    await updateAnnouncement(id, { isActive });
  };

  const handleDeleteAnnouncement = async (id: string) => {
    if (confirm('Are you sure you want to delete this announcement?')) {
      await deleteAnnouncement(id);
    }
  };

  const getAnnouncementIcon = (type: Announcement['type']) => {
    switch (type) {
      case 'success': return 'checkCircle';
      case 'warning': return 'exclamationTriangle';
      case 'error': return 'xCircle';
      default: return 'informationCircle';
    }
  };

  const getAnnouncementColor = (type: Announcement['type']) => {
    switch (type) {
      case 'success': return 'green';
      case 'warning': return 'yellow';
      case 'error': return 'red';
      default: return 'blue';
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with Create Button */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-neutral-darkest">Communication Center</h2>
        <Button
          onClick={() => setShowCreateForm(!showCreateForm)}
          className="px-6"
        >
          <Icon name="speakerphone" className="w-5 h-5 mr-2" />
          Create Announcement
        </Button>
      </div>

      {/* Create Announcement Form */}
      {showCreateForm && (
        <Card className="p-6">
          <h3 className="text-xl font-semibold text-neutral-darkest mb-4 flex items-center">
            <Icon name="plusCircle" className="w-6 h-6 mr-2" />
            New Announcement
          </h3>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Title"
                value={newAnnouncement.title}
                onChange={(e) => setNewAnnouncement(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Important announcement..."
              />
              <Select
                label="Type"
                value={newAnnouncement.type}
                onChange={(e) => setNewAnnouncement(prev => ({ ...prev, type: e.target.value as Announcement['type'] }))}
                options={[
                  { value: 'info', label: 'Information' },
                  { value: 'success', label: 'Success' },
                  { value: 'warning', label: 'Warning' },
                  { value: 'error', label: 'Error' }
                ]}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-neutral-dark mb-1">
                Message
              </label>
              <textarea
                className="block w-full px-3 py-2 border border-neutral-light rounded-md focus:outline-none focus:ring-primary focus:border-primary"
                rows={3}
                value={newAnnouncement.message}
                onChange={(e) => setNewAnnouncement(prev => ({ ...prev, message: e.target.value }))}
                placeholder="Enter your announcement message..."
              />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-neutral-dark mb-1">
                  Target Roles (leave empty for all users)
                </label>
                <div className="space-y-2">
                  {Object.values(UserRole).map(role => (
                    <label key={role} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={newAnnouncement.targetRoles.includes(role)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setNewAnnouncement(prev => ({
                              ...prev,
                              targetRoles: [...prev.targetRoles, role]
                            }));
                          } else {
                            setNewAnnouncement(prev => ({
                              ...prev,
                              targetRoles: prev.targetRoles.filter(r => r !== role)
                            }));
                          }
                        }}
                        className="mr-2"
                      />
                      <span className="text-sm text-neutral-dark">{role}s</span>
                    </label>
                  ))}
                </div>
              </div>
              
              <Input
                label="Expires At (optional)"
                type="datetime-local"
                value={newAnnouncement.expiresAt}
                onChange={(e) => setNewAnnouncement(prev => ({ ...prev, expiresAt: e.target.value }))}
              />
            </div>
            
            <div className="flex justify-end space-x-3">
              <Button
                onClick={() => setShowCreateForm(false)}
                variant="outline"
              >
                Cancel
              </Button>
              <Button
                onClick={handleCreateAnnouncement}
                isLoading={isLoading}
              >
                Create Announcement
              </Button>
            </div>
          </div>
        </Card>
      )}

      {/* Announcements List */}
      <div className="space-y-4">
        <h3 className="text-xl font-semibold text-neutral-darkest flex items-center">
          <Icon name="bellAlert" className="w-6 h-6 mr-2" />
          Active Announcements ({announcements.filter(a => a.isActive).length})
        </h3>
        
        {announcements.length === 0 ? (
          <Card className="p-8 text-center">
            <Icon name="speakerphone" className="w-16 h-16 text-neutral-light mx-auto mb-4" />
            <h4 className="text-lg font-medium text-neutral-dark mb-2">No Announcements</h4>
            <p className="text-neutral-dark">Create your first announcement to communicate with users.</p>
          </Card>
        ) : (
          <div className="space-y-4">
            {announcements.map((announcement) => {
              const color = getAnnouncementColor(announcement.type);
              const icon = getAnnouncementIcon(announcement.type);
              const isExpired = announcement.expiresAt && new Date(announcement.expiresAt) < new Date();
              
              return (
                <Card key={announcement.id} className={`p-6 border-l-4 border-${color}-500`}>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center mb-2">
                        <Icon name={icon} className={`w-5 h-5 text-${color}-500 mr-2`} />
                        <h4 className="text-lg font-semibold text-neutral-darkest">
                          {announcement.title}
                        </h4>
                        {!announcement.isActive && (
                          <span className="ml-2 px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                            Inactive
                          </span>
                        )}
                        {isExpired && (
                          <span className="ml-2 px-2 py-1 bg-red-100 text-red-600 text-xs rounded">
                            Expired
                          </span>
                        )}
                      </div>
                      
                      <p className="text-neutral-dark mb-3">{announcement.message}</p>
                      
                      <div className="flex items-center space-x-4 text-sm text-neutral-dark">
                        <span>Created: {new Date(announcement.createdAt).toLocaleDateString()}</span>
                        {announcement.expiresAt && (
                          <span>Expires: {new Date(announcement.expiresAt).toLocaleDateString()}</span>
                        )}
                        {announcement.targetRoles.length > 0 && (
                          <span>Target: {announcement.targetRoles.join(', ')}</span>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex space-x-2 ml-4">
                      <Button
                        onClick={() => handleToggleAnnouncement(announcement.id, !announcement.isActive)}
                        variant="outline"
                        size="sm"
                      >
                        {announcement.isActive ? 'Deactivate' : 'Activate'}
                      </Button>
                      <Button
                        onClick={() => handleDeleteAnnouncement(announcement.id)}
                        variant="danger"
                        size="sm"
                      >
                        <Icon name="trash" className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </Card>
              );
            })}
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <Card className="p-6">
        <h3 className="text-xl font-semibold text-neutral-darkest mb-4 flex items-center">
          <Icon name="envelope" className="w-6 h-6 mr-2" />
          Quick Communication
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Button
            onClick={() => {
              setNewAnnouncement(prev => ({
                ...prev,
                title: 'Welcome New Users!',
                message: 'Welcome to The MusicArt Club! We\'re excited to have you join our creative community.',
                type: 'success'
              }));
              setShowCreateForm(true);
            }}
            variant="outline"
            className="h-auto p-4 text-left"
          >
            <div>
              <h4 className="font-medium text-neutral-darkest">Welcome Message</h4>
              <p className="text-sm text-neutral-dark mt-1">Create a welcome announcement for new users</p>
            </div>
          </Button>
          
          <Button
            onClick={() => {
              setNewAnnouncement(prev => ({
                ...prev,
                title: 'Maintenance Notice',
                message: 'Scheduled maintenance will occur tonight from 2-4 AM EST. The site may be temporarily unavailable.',
                type: 'warning'
              }));
              setShowCreateForm(true);
            }}
            variant="outline"
            className="h-auto p-4 text-left"
          >
            <div>
              <h4 className="font-medium text-neutral-darkest">Maintenance Notice</h4>
              <p className="text-sm text-neutral-dark mt-1">Notify users about scheduled maintenance</p>
            </div>
          </Button>
          
          <Button
            onClick={() => {
              setNewAnnouncement(prev => ({
                ...prev,
                title: 'New Feature Available!',
                message: 'Check out our latest feature updates and improvements to enhance your experience.',
                type: 'info'
              }));
              setShowCreateForm(true);
            }}
            variant="outline"
            className="h-auto p-4 text-left"
          >
            <div>
              <h4 className="font-medium text-neutral-darkest">Feature Update</h4>
              <p className="text-sm text-neutral-dark mt-1">Announce new features and updates</p>
            </div>
          </Button>
        </div>
      </Card>
    </div>
  );
};
