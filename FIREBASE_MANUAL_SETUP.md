# Firebase Manual Setup Steps

Since your app is hosted on Netlify, you only need to configure the Firebase backend services. Here are the manual steps to complete your Firebase setup:

## 🔐 Authentication Setup

### 1. Enable Authentication Providers

Go to [Firebase Console > Authentication > Sign-in method](https://console.firebase.google.com/project/musicart-7641d/authentication/providers)

**Enable these providers:**

#### Email/Password
- ✅ **Enable** Email/Password authentication
- ✅ **Enable** Email link (passwordless sign-in) - optional
- Set email templates for verification, password reset, etc.

#### Google Sign-In (Recommended)
- ✅ **Enable** Google provider
- Add your Netlify domain to authorized domains
- Configure OAuth consent screen

#### Optional Providers
- **Facebook**: For social login
- **Twitter**: For social login
- **GitHub**: For developer-friendly login

### 2. Configure Authorized Domains

Add your Netlify domain to authorized domains:
- Go to Authentication > Settings > Authorized domains
- Add your Netlify URL (e.g., `your-app-name.netlify.app`)
- Add `localhost` for development

### 3. Email Templates

Customize email templates in Authentication > Templates:
- **Email verification**
- **Password reset**
- **Email address change**

## 🗄️ Firestore Database Setup

### 1. Create Database
- Go to [Firestore Database](https://console.firebase.google.com/project/musicart-7641d/firestore)
- Click "Create database"
- Choose "Start in production mode" (rules are already deployed)
- Select a location (choose closest to your users)

### 2. Verify Collections
The app will automatically create these collections when users interact:
- `users` - User profiles
- `posts` - Artist posts
- `events` - Educator events
- `lessons` - Educator lessons
- `messages` - Direct messages
- `comments` - Post comments
- `siteSettings` - App configuration
- `announcements` - Admin announcements

## 💾 Storage Setup

### 1. Enable Storage
- Go to [Storage](https://console.firebase.google.com/project/musicart-7641d/storage)
- Click "Get started"
- Choose same location as Firestore
- Storage rules are already deployed

### 2. Configure CORS (if needed)
If you encounter CORS issues, create a `cors.json` file:
```json
[
  {
    "origin": ["https://your-app.netlify.app", "http://localhost:5173"],
    "method": ["GET", "POST", "PUT", "DELETE"],
    "maxAgeSeconds": 3600
  }
]
```

Then run: `gsutil cors set cors.json gs://musicart-7641d.firebasestorage.app`

## 🔧 Environment Variables for Netlify

### 1. Set Environment Variables in Netlify

Go to your Netlify site settings > Environment variables and add:

```
VITE_FIREBASE_API_KEY=AIzaSyBhVCh-UFBHIYdnLQ0C37GHFSCGpmQOHc8
VITE_FIREBASE_AUTH_DOMAIN=musicart-7641d.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=musicart-7641d
VITE_FIREBASE_STORAGE_BUCKET=musicart-7641d.firebasestorage.app
VITE_FIREBASE_MESSAGING_SENDER_ID=************
VITE_FIREBASE_APP_ID=1:************:web:4117b804da97bb8bc55f10
VITE_FIREBASE_MEASUREMENT_ID=G-38F2VTX3SN
NODE_ENV=production
```

### 2. Update Build Settings

In Netlify site settings > Build & deploy:
- **Build command**: `npm run build`
- **Publish directory**: `dist`
- **Node version**: `18` (in environment variables)

## 📊 Analytics Setup

### 1. Enable Google Analytics
- Go to [Analytics](https://console.firebase.google.com/project/musicart-7641d/analytics)
- Link to Google Analytics account
- Configure events and conversions

### 2. Performance Monitoring
- Go to Performance > Get started
- Enable performance monitoring
- Add custom traces if needed

## 🛡️ Security Configuration

### 1. App Check (Recommended)
- Go to App Check
- Enable reCAPTCHA v3 for web
- Add your Netlify domain

### 2. Security Rules Review
Rules are already deployed, but you can review them:
- **Firestore rules**: `firestore.rules`
- **Storage rules**: `storage.rules`

## 🚀 Testing Your Setup

### 1. Test Authentication
- Try signing up with email/password
- Test Google sign-in (if enabled)
- Verify email verification works

### 2. Test Database Operations
- Create a post as an Artist
- Create an event as an Educator
- Send a message as any user
- Test admin functions

### 3. Test File Uploads
- Upload profile photo
- Upload banner image
- Upload post media

## 🔍 Monitoring & Maintenance

### 1. Set Up Alerts
- Go to Monitoring
- Set up alerts for:
  - High error rates
  - Unusual traffic patterns
  - Storage quota warnings

### 2. Backup Strategy
- Enable automatic backups in Firestore
- Set up export schedules
- Monitor storage usage

### 3. Performance Optimization
- Monitor query performance
- Optimize indexes based on usage
- Review security rules efficiency

## 🆘 Troubleshooting

### Common Issues:

1. **CORS Errors**: Configure CORS for Storage
2. **Auth Domain Issues**: Add Netlify domain to authorized domains
3. **Permission Denied**: Check Firestore security rules
4. **Build Failures**: Verify environment variables in Netlify

### Debug Tools:
- Firebase Console > Usage and billing
- Browser Developer Tools > Network tab
- Firebase Emulator Suite for local testing

## 📞 Support Resources

- [Firebase Documentation](https://firebase.google.com/docs)
- [Firebase Console](https://console.firebase.google.com/project/musicart-7641d)
- [Netlify Documentation](https://docs.netlify.com/)
- [GitHub Issues](https://github.com/firebase/firebase-js-sdk/issues)

## ✅ Completion Checklist

- [ ] Authentication providers enabled
- [ ] Authorized domains configured
- [ ] Firestore database created
- [ ] Storage enabled
- [ ] Environment variables set in Netlify
- [ ] App deployed and tested
- [ ] Analytics configured
- [ ] Monitoring set up
- [ ] Backup strategy implemented

Once you complete these steps, your MusicArt Club will be fully functional with Firebase backend and Netlify hosting!
